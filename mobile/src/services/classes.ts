import type { Class, CreateClassDto, UpdateClassDto } from '../types/classes';
import { getHttpClient } from './httpClient';
import { API_ENDPOINTS } from '../config/api';

/**
 * Classes service aligned with backend/routes/api/classe.js
 * Endpoints:
 *  - GET    /api/classe
 *  - POST   /api/classe
 *  - PUT    /api/classe/:oldNomClasse
 *  - DELETE /api/classe/:nomClasse
 *
 * Note: Backend returns status 200 with empty body for create/update/delete.
 */
const http = () => getHttpClient();

const BASE = (API_ENDPOINTS as any)?.ACADEMIC?.CLASSES || '/api/classe';

export const classesService = {
  async list(): Promise<Class[]> {
    const res = await http().get<any>(BASE);
    // Backend returns array of rows
    const data = res?.data ?? res ?? [];
    return Array.isArray(data) ? data : [];
  },

  async create(dto: CreateClassDto): Promise<void> {
    await http().post<void>(BASE, dto);
  },

  async update(oldNomClasse: string, dto: UpdateClassDto): Promise<void> {
    const path = `${BASE}/${encodeURIComponent(oldNomClasse)}`;
    await http().put<void>(path, dto);
  },

  async remove(nomClasse: string): Promise<void> {
    const path = `${BASE}/${encodeURIComponent(nomClasse)}`;
    await http().delete<void>(path);
  },
};
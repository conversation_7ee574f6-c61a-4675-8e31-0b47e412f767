/**
 * Message Service for NS School Manager Mobile App
 * Handles all messaging functionality for admin users
 */

import { getHttpClient } from './httpClient';
import { API_ENDPOINTS } from '../config/api';
import type {
  ApiResponse,
  Message,
  Conversation,
  ComposeMessageData,
  AdminMessageStats,
  MessageRecipient
} from '../types/api';

const http = () => getHttpClient();

export const messageService = {
  /**
   * Get conversations for the current user (works for all roles)
   */
  async getConversations(): Promise<Conversation[]> {
    try {
      const response = await http().get<any>(API_ENDPOINTS.MOBILE_MESSAGES.CONVERSATIONS);
      // Handle the mobile API response format: { success: true, data: { conversations: [...], stats: {...} } }
      return response?.data?.conversations || [];
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw new Error('Erreur lors du chargement des conversations');
    }
  },

  /**
   * Get admin conversations (alias for backward compatibility)
   */
  async getAdminConversations(): Promise<Conversation[]> {
    return this.getConversations();
  },

  /**
   * Get messages for a specific conversation
   */
  async getConversationMessages(participantId: string): Promise<Message[]> {
    try {
      // Mobile API route returns { success: true, data: { messages: [...] } }
      const response = await http().get<any>(`/api/mobile/messages/conversation/${encodeURIComponent(participantId)}`);
      return response?.data?.messages || response?.data?.data?.messages || [];
    } catch (error) {
      console.error('Error fetching conversation messages:', error);
      throw new Error('Erreur lors du chargement des messages');
    }
  },

  /**
   * Send a message
   */
  async sendMessage(messageData: ComposeMessageData): Promise<void> {
    try {
      await http().post<any>(API_ENDPOINTS.MOBILE_MESSAGES.SEND, messageData);
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Erreur lors de l\'envoi du message');
    }
  },

  /**
   * Send a quick reply to an existing conversation
   */
  async sendQuickReply(participantId: string, contenu: string): Promise<void> {
    try {
      // Mobile API route expects participant_id and contenu
      await http().post<any>('/api/mobile/messages/reply', {
        participant_id: participantId,
        contenu: contenu?.trim(),
      });
    } catch (error) {
      console.error('Error sending quick reply:', error);
      throw new Error('Erreur lors de l\'envoi de la réponse');
    }
  },

  /**
   * Get available message recipients and classes
   */
  async getMessageRecipients(): Promise<{ recipients: MessageRecipient[]; classes: { nom_classe: string }[] }> {
    try {
      const response = await http().get<any>(API_ENDPOINTS.MOBILE_MESSAGES.RECIPIENTS);
      return {
        recipients: response?.data?.recipients || [],
        classes: response?.data?.classes || []
      };
    } catch (error) {
      console.error('Error fetching message recipients:', error);
      throw new Error('Erreur lors du chargement des destinataires');
    }
  },

  /**
   * Get available classes for announcements (alias for backward compatibility)
   */
  async getAvailableClasses(): Promise<{ nom_classe: string }[]> {
    try {
      const { classes } = await this.getMessageRecipients();
      return classes;
    } catch (error) {
      console.error('Error fetching classes:', error);
      throw new Error('Erreur lors du chargement des classes');
    }
  },

  /**
   * Get admin message statistics
   */
  async getAdminMessageStats(): Promise<AdminMessageStats> {
    try {
      const conversations = await messageService.getAdminConversations();
      
      const stats: AdminMessageStats = {
        unread: 0,
        private: 0,
        announcements: 0,
        professors: 0
      };

      conversations.forEach(conv => {
        stats.unread += conv.unread_count;
        if (conv.type === 'privé') {
          stats.private++;
        } else if (conv.type === 'annonce') {
          stats.announcements++;
        }
        if (conv.participant_role === 'professeur') {
          stats.professors++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error calculating message stats:', error);
      return {
        unread: 0,
        private: 0,
        announcements: 0,
        professors: 0
      };
    }
  },

  /**
   * Mark messages as read
   * Note: No mobile endpoint provided in current backend; keep as no-op to avoid errors.
   */
  async markMessagesAsRead(participantId: string): Promise<void> {
    try {
      // TODO: Implement when backend route exists, e.g., /api/mobile/messages/conversation/:id/read-all
      return;
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  },
};

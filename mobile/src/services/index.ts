/**
 * API Services Index - Initialization and Export
 * Central initialization point for all API services
 */

import { createHttpClient } from './httpClient';
import { AuthService } from './authService';
import { ApiServices } from './apiService';
import { API_CONFIG } from '../config/api';
import { Logger } from '../utils/logger';

/**
 * Initialize all API services
 * Call this once when the app starts
 */
export const initializeApiServices = (): void => {
  try {
    Logger.info('Initializing API services...', { baseURL: API_CONFIG.baseURL });
    
    // Initialize HTTP client
    createHttpClient(API_CONFIG);
    
    // Initialize auth service (singleton)
    AuthService.getInstance();
    
    Logger.info('API services initialized successfully');
  } catch (error) {
    Logger.error('Failed to initialize API services', error);
    throw error;
  }
};

/**
 * Get all API services
 */
export const getApiServices = () => {
  return {
    auth: AuthService.getInstance(),
    user: ApiServices.user(),
    academic: ApiServices.academic(),
    communication: ApiServices.communication(),
    schedule: ApiServices.schedule(),
  };
};
// Admin Timetable (Emploi du Temps) – Mobile service (admin-only scope)
export const getAdminTimetableService = () => ApiServices.schedule(); // temporary alias until dedicated service is added

// Re-export all services and types for easy access
export { AuthService } from './authService';
export { TokenManager } from './tokenManager';
export { UserStorage } from './userStorage';
export { HttpClient, getHttpClient } from './httpClient';
export { 
  getUserService,
  getAcademicService,
  getCommunicationService,
  getScheduleService,
  ApiServices
} from './apiService';

// Re-export types
export * from '../types/api';

// Re-export configuration
export { API_CONFIG, API_ENDPOINTS, CONFIG } from '../config/api';

// Re-export utilities
export { Logger } from '../utils/logger';

import { getHttpClient } from './httpClient';

export type UIScheduleSession = {
  id: number | string;
  subject: string;
  start: string; // HH:mm
  end: string;   // HH:mm
  day: string;   // e.g., LUNDI
  className: string;
  room?: string;
};

export type CreateUpdateCreneauDTO = {
  nom_classe: string;
  matiere_id: number | string;
  jour_semaine: string;
  heure_debut: string; // HH:mm
  heure_fin: string;   // HH:mm
  salle_id?: number | string;
  salle?: string;
};

export type VerifyConflictDTO = CreateUpdateCreneauDTO & { exclude_id?: number | string };

export type ConflictResponse = {
  aConflits: boolean;
  conflitsClasse?: Array<any>;
  conflitsProfesseur?: Array<any>;
};

export type Classe = { nom_classe: string; id?: number | string };
export type Subject = { id: number | string; nom: string; code?: string };
export type Slot = { start: string; end: string; label?: string };

// Minimal admin schedule service for initial step, now extended with subjects and CRUD
const encode = encodeURIComponent;

export const ScheduleAdminService = {
  async listClasses(): Promise<Classe[]> {
    const http = getHttpClient();
    const data = await http.get<any>('/api/emploi_temps/classes');
    if (Array.isArray(data)) return data as Classe[];
    if (data && Array.isArray(data.data)) return data.data as Classe[];
    if (data && Array.isArray(data.classes)) return data.classes as Classe[];
    return [];
  },

  async listSubjects(nomClasse?: string): Promise<Subject[]> {
    const http = getHttpClient();
    const endpoint = nomClasse
      ? `/api/emploi_temps/matieres?nom_classe=${encode(nomClasse)}`
      : '/api/emploi_temps/matieres';
    const raw = await http.get<any>(endpoint);
    if (Array.isArray(raw)) return raw as Subject[];
    if (raw && Array.isArray(raw.data)) return raw.data as Subject[];
    if (raw && Array.isArray(raw.matieres)) return raw.matieres as Subject[];
    return [];
  },

  async getSlots(): Promise<Slot[]> {
    const http = getHttpClient();
    const data = await http.get<any>('/api/emploi_temps/creneaux-disponibles');
    if (Array.isArray(data)) return data as Slot[];
    if (data && Array.isArray(data.creneauxHoraires)) return data.creneauxHoraires as Slot[];
    if (data && Array.isArray(data.data)) return data.data as Slot[];
    return [];
  },

  async getPlanningByClass(nomClasse: string): Promise<UIScheduleSession[]> {
    const http = getHttpClient();
    const raw = await http.get<any>(`/api/emploi_temps/planning/${encode(nomClasse)}`);
    const entries: any[] =
      (raw && Array.isArray(raw.creneaux) && raw.creneaux) ||
      (raw && Array.isArray(raw.data) && raw.data) ||
      (Array.isArray(raw) ? raw : []);
    return entries.map((it: any) => ({
      id: it.id ?? it._id ?? `${it.jour_semaine}-${it.heure_debut}-${it.matiere_id}`,
      subject: it.nom_matiere ?? it.matiere_nom ?? it.matiere ?? '',
      start: it.heure_debut,
      end: it.heure_fin,
      day: it.jour_semaine,
      className: it.nom_classe ?? nomClasse,
      room: it.salle_nom ?? it.salle ?? it.salle_id ?? undefined,
    })) as UIScheduleSession[];
  },

  async verifyConflicts(dto: VerifyConflictDTO, options?: { signal?: AbortSignal }): Promise<ConflictResponse> {
    const http = getHttpClient();
    const raw = await http.post<any>('/api/emploi_temps/verifier-conflits', dto, { signal: options?.signal } as any);
    // Accept either raw structure or { data: {...} }
    const body = raw && typeof raw === 'object' && 'data' in raw ? raw.data : raw;
    return {
      aConflits: !!body?.aConflits,
      conflitsClasse: body?.conflitsClasse ?? [],
      conflitsProfesseur: body?.conflitsProfesseur ?? [],
    } as ConflictResponse;
  },

  async createCreneau(dto: CreateUpdateCreneauDTO): Promise<{ id: number | string }> {
    const http = getHttpClient();
    const raw = await http.post<any>('/api/emploi_temps', dto);
    // Accept common envelopes
    if (raw && typeof raw === 'object') {
      if ('id' in raw) return { id: raw.id as number | string };
      if ('data' in raw && raw.data && typeof raw.data === 'object' && 'id' in raw.data) {
        return { id: (raw.data as any).id as number | string };
      }
    }
    // Fallback: no id known
    return { id: (Math.random() * 1e9) | 0 };
  },

  async updateCreneau(id: number | string, dto: CreateUpdateCreneauDTO): Promise<void> {
    const http = getHttpClient();
    await http.put<any>(`/api/emploi_temps/${encode(String(id))}`, dto);
  },

  async deleteCreneau(id: number | string): Promise<void> {
    const http = getHttpClient();
    await http.delete<any>(`/api/emploi_temps/${encode(String(id))}`);
  },
};
import { getApiUrl } from '../config/api';
import type { Course, CreateCourseDto, UpdateCourseDto, TeacherOption, ClassOption } from '../types/courses';

async function http(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, body?: any) {
  try {
    const res = await fetch(getApiUrl(url), {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: body ? JSON.stringify(body) : undefined,
    });
    if (!res.ok) {
      const data = await res.json().catch(() => null);
      const message = data?.message || `HTTP ${res.status}`;
      throw { message };
    }
    if (method === 'GET') {
      return res.json();
    }
    return undefined;
  } catch (err: any) {
    const message = err?.message || 'Une erreur est survenue';
    throw { message };
  }
}

export const coursesService = {
  async list(): Promise<Course[]> {
    const data = await http('GET', '/api/cour');
    return data as Course[];
  },

  async listTeachers(): Promise<TeacherOption[]> {
    const data = await http('GET', '/api/cour/professeurs');
    return data as TeacherOption[];
  },

  // Optional fallback; prefer using classesService.list() in UI where possible
  async listClasses(): Promise<ClassOption[]> {
    const data = await http('GET', '/api/cour/classes');
    return data as ClassOption[];
  },

  async create(dto: CreateCourseDto): Promise<void> {
    await http('POST', '/api/cour', dto);
  },

  async update(id: number, dto: UpdateCourseDto): Promise<void> {
    await http('PUT', `/api/cour/${id}`, dto);
  },

  async remove(id: number): Promise<void> {
    await http('DELETE', `/api/cour/${id}`);
  },
};

export default coursesService;
// Types for Classes management aligned with backend/routes/api/classe.js

export type NomClasse = string;

export interface Class {
  nom_classe: NomClasse;
  niveau: string;
  annee_scolaire: string;
}

export interface CreateClassDto {
  nom_classe: NomClasse;
  niveau: string;
  annee_scolaire: string;
}

export interface UpdateClassDto {
  nom_classe: NomClasse;
  niveau: string;
  annee_scolaire: string;
}

// Backend returns 200 with empty body for create/update/delete; list returns array of Class
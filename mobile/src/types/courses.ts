export interface Course {
  matiere_id: number;
  nom: string;
  professeur_matricule?: string | null;
  professeur_nom?: string | null;
  professeur_prenom?: string | null;
  nom_classe: string;
  niveau?: unknown;
  annee_scolaire?: unknown;
}

export interface CreateCourseDto {
  nom: string;
  professeur_matricule?: string | null;
  nom_classe: string;
}

export interface UpdateCourseDto {
  nom: string;
  professeur_matricule?: string | null;
  nom_classe: string;
}

export interface TeacherOption {
  matricule: string;
  nom: string;
  prenom: string;
}

export interface ClassOption {
  nom_classe: string;
}
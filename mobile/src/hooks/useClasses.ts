import { useEffect, useState } from 'react';
import type { Class, CreateClassDto, UpdateClassDto } from '../types/classes';
import { classesService } from '../services/classes';
import { Logger } from '../utils/logger';

type Snapshot = { data: Class[] | null; loading: boolean; error: string | null };

const cache: {
  data: Class[] | null;
  loading: boolean;
  error: string | null;
  subscribers: Set<() => void>;
} = {
  data: null,
  loading: false,
  error: null,
  subscribers: new Set(),
};

function notifySubscribers() {
  cache.subscribers.forEach((cb) => {
    try {
      cb();
    } catch (e) {
      Logger?.error?.('useClasses subscriber error', e);
    }
  });
}

function setLoading(v: boolean) {
  cache.loading = v;
  notifySubscribers();
}

function setError(msg: string | null) {
  cache.error = msg;
  notifySubscribers();
}

function setData(d: Class[] | null) {
  cache.data = d;
  notifySubscribers();
}

function getSnapshot(): Snapshot {
  return { data: cache.data, loading: cache.loading, error: cache.error };
}

export async function fetchClasses() {
  // Avoid overlapping fetches if already in-flight
  if (!cache.loading) setLoading(true);
  setError(null);
  try {
    const rows = await classesService.list();
    setData(rows);
  } catch (e: any) {
    const message = e?.message || 'Erreur lors du chargement des classes';
    setError(message);
    Logger?.error?.('fetchClasses failed', e);
  } finally {
    setLoading(false);
  }
}

export function useClasses() {
  const [snap, setSnap] = useState<Snapshot>(getSnapshot());

  useEffect(() => {
    const subscriber = () => setSnap(getSnapshot());
    cache.subscribers.add(subscriber);

    // initial load if not present
    if (!cache.data && !cache.loading) {
      void fetchClasses();
    } else {
      // ensure immediate sync
      setSnap(getSnapshot());
    }

    return () => {
      cache.subscribers.delete(subscriber);
    };
  }, []);

  return {
    data: snap.data,
    loading: snap.loading,
    error: snap.error,
    refetch: fetchClasses,
  };
}

function useMutationState() {
  const [loading, setLoading] = useState(false);
  const [error, setErr] = useState<string | null>(null);
  return { loading, setLoading, error, setErr };
}

export function useCreateClass() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (dto: CreateClassDto) => {
    setErr(null);
    setLoading(true);
    try {
      await classesService.create(dto);
      await fetchClasses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la création de la classe';
      setErr(message);
      Logger?.error?.('create class failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}

export function useUpdateClass() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (oldNomClasse: string, dto: UpdateClassDto) => {
    setErr(null);
    setLoading(true);
    try {
      await classesService.update(oldNomClasse, dto);
      await fetchClasses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la mise à jour de la classe';
      setErr(message);
      Logger?.error?.('update class failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}

export function useDeleteClass() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (nomClasse: string) => {
    setErr(null);
    setLoading(true);
    try {
      await classesService.remove(nomClasse);
      await fetchClasses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la suppression de la classe';
      setErr(message);
      Logger?.error?.('delete class failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}
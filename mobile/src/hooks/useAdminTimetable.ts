import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ScheduleAdminService, type UIScheduleSession, type Classe, type Slot } from '../services/scheduleAdminService';

type AsyncState = {
  loading: boolean;
  error?: string;
};

export function useAdminTimetable() {
  const [classes, setClasses] = useState<Classe[]>([]);
  const [slots, setSlots] = useState<Slot[]>([]);
  const [planning, setPlanning] = useState<UIScheduleSession[]>([]);

  const [selectedClass, setSelectedClass] = useState<string | undefined>(undefined);
  const [selectedDay, setSelectedDay] = useState<string | undefined>(undefined);

  const [classesState, setClassesState] = useState<AsyncState>({ loading: false });
  const [slotsState, setSlotsState] = useState<AsyncState>({ loading: false });
  const [planningState, setPlanningState] = useState<AsyncState>({ loading: false });

  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const loadClasses = useCallback(async () => {
    setClassesState({ loading: true });
    try {
      const data = await ScheduleAdminService.listClasses();
      if (mountedRef.current) {
        setClasses(data);
        setClassesState({ loading: false });
      }
    } catch (e: any) {
      if (mountedRef.current) {
        setClassesState({ loading: false, error: e?.message || 'Failed to load classes' });
      }
    }
  }, []);

  const loadSlots = useCallback(async () => {
    setSlotsState({ loading: true });
    try {
      const data = await ScheduleAdminService.getSlots();
      if (mountedRef.current) {
        setSlots(data);
        setSlotsState({ loading: false });
      }
    } catch (e: any) {
      if (mountedRef.current) {
        setSlotsState({ loading: false, error: e?.message || 'Failed to load time slots' });
      }
    }
  }, []);

  const refreshPlanning = useCallback(async () => {
    if (!selectedClass) {
      setPlanning([]);
      return;
    }
    setPlanningState({ loading: true });
    try {
      const data = await ScheduleAdminService.getPlanningByClass(selectedClass);
      if (mountedRef.current) {
        setPlanning(data);
        setPlanningState({ loading: false });
      }
    } catch (e: any) {
      if (mountedRef.current) {
        setPlanningState({ loading: false, error: e?.message || 'Failed to load planning' });
      }
    }
  }, [selectedClass]);

  useEffect(() => {
    // initial bootstrap
    loadClasses();
    loadSlots();
  }, [loadClasses, loadSlots]);

  useEffect(() => {
    // refresh when class changes
    refreshPlanning();
  }, [refreshPlanning]);

  const filteredPlanning = useMemo(() => {
    if (!selectedDay) return planning;
    return planning.filter((p) => p.day === selectedDay);
  }, [planning, selectedDay]);

  const deletePlanningEntry = useCallback(async (id: number | string) => {
    try {
      await ScheduleAdminService.deleteCreneau(id);
      if (mountedRef.current) {
        setPlanning((prev) => prev.filter((p) => p.id !== id));
      }
    } catch (e: any) {
      console.error('Failed to delete planning entry:', e);
      // Optionally, re-throw or handle the error in the UI
      throw e;
    }
  }, []);

  return {
    // data
    classes,
    slots,
    planning: filteredPlanning,

    // selections
    selectedClass,
    setSelectedClass,
    selectedDay,
    setSelectedDay,

    // states
    loadingClasses: classesState.loading,
    classesError: classesState.error,
    loadingSlots: slotsState.loading,
    slotsError: slotsState.error,
    loadingPlanning: planningState.loading,
    planningError: planningState.error,

    // actions
    loadClasses,
    loadSlots,
    refreshPlanning,
    deletePlanningEntry,
  };
}
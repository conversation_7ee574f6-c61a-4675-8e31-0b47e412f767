import { useEffect, useState } from 'react';
import type { Course, CreateCourseDto, UpdateCourseDto } from '../types/courses';
import { coursesService } from '../services/courses';
import { Logger } from '../utils/logger';

type Snapshot = { data: Course[] | null; loading: boolean; error: string | null };

const cache: {
  data: Course[] | null;
  loading: boolean;
  error: string | null;
  subscribers: Set<() => void>;
} = {
  data: null,
  loading: false,
  error: null,
  subscribers: new Set(),
};

function notifySubscribers() {
  cache.subscribers.forEach((cb) => {
    try {
      cb();
    } catch (e) {
      Logger?.error?.('useCourses subscriber error', e);
    }
  });
}

function setLoading(v: boolean) {
  cache.loading = v;
  notifySubscribers();
}

function setError(msg: string | null) {
  cache.error = msg;
  notifySubscribers();
}

function setData(d: Course[] | null) {
  cache.data = d;
  notifySubscribers();
}

function getSnapshot(): Snapshot {
  return { data: cache.data, loading: cache.loading, error: cache.error };
}

export async function fetchCourses() {
  if (!cache.loading) setLoading(true);
  setError(null);
  try {
    const rows = await coursesService.list();
    setData(rows);
  } catch (e: any) {
    const message = e?.message || 'Erreur lors du chargement des cours';
    setError(message);
    Logger?.error?.('fetchCourses failed', e);
  } finally {
    setLoading(false);
  }
}

export function useCourses() {
  const [snap, setSnap] = useState<Snapshot>(getSnapshot());

  useEffect(() => {
    const subscriber = () => setSnap(getSnapshot());
    cache.subscribers.add(subscriber);

    if (!cache.data && !cache.loading) {
      void fetchCourses();
    } else {
      setSnap(getSnapshot());
    }

    return () => {
      cache.subscribers.delete(subscriber);
    };
  }, []);

  return {
    data: snap.data,
    loading: snap.loading,
    error: snap.error,
    refetch: fetchCourses,
  };
}

function useMutationState() {
  const [loading, setLoading] = useState(false);
  const [error, setErr] = useState<string | null>(null);
  return { loading, setLoading, error, setErr };
}

export function useCreateCourse() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (dto: CreateCourseDto) => {
    setErr(null);
    setLoading(true);
    try {
      await coursesService.create(dto);
      await fetchCourses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la création du cours';
      setErr(message);
      Logger?.error?.('create course failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}

export function useUpdateCourse() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (id: number, dto: UpdateCourseDto) => {
    setErr(null);
    setLoading(true);
    try {
      await coursesService.update(id, dto);
      await fetchCourses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la mise à jour du cours';
      setErr(message);
      Logger?.error?.('update course failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}

export function useDeleteCourse() {
  const { loading, setLoading, error, setErr } = useMutationState();

  const mutate = async (id: number) => {
    setErr(null);
    setLoading(true);
    try {
      await coursesService.remove(id);
      await fetchCourses();
    } catch (e: any) {
      const message = e?.message || 'Erreur lors de la suppression du cours';
      setErr(message);
      Logger?.error?.('delete course failed', e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}
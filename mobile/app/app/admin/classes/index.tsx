import React, { useCallback, useMemo, useState } from 'react';
import { View, Text, FlatList, RefreshControl, ActivityIndicator, TouchableOpacity, useColorScheme, StyleSheet, Alert, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import type { Class } from '../../../../src/types/classes';
import { classesService } from '../../../../src/services/classes';

export default function ClassesListScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [data, setData] = useState<Class[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [q, setQ] = useState('');

  const load = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const res = await classesService.list();
      setData(Array.isArray(res) ? res : []);
    } catch (e: any) {
      const msg = e?.response?.data?.message || e?.message || 'Erreur de chargement des classes';
      setError(msg);
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    load();
  }, [load]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await load();
    } finally {
      setRefreshing(false);
    }
  }, [load]);

  const filtered = useMemo(() => {
    const query = q.trim().toLowerCase();
    if (!query) return data;
    return data.filter(item => {
      const hay = `${item.nom_classe} ${item.niveau} ${item.annee_scolaire}`.toLowerCase();
      return hay.includes(query);
    });
  }, [q, data]);

  const confirmDelete = (nom_classe: string) => {
    Alert.alert(
      'Confirmer la suppression',
      `Supprimer la classe "${nom_classe}" ? Cette action est irréversible.`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await classesService.remove(nom_classe);
              setData(prev => prev.filter(c => c.nom_classe !== nom_classe));
            } catch (e: any) {
              const msg = e?.response?.data?.message || e?.message || 'Erreur lors de la suppression';
              Alert.alert('Erreur', msg);
            }
          }
        }
      ]
    );
  };

  const renderItem = ({ item }: { item: Class }) => (
    <View
      style={[
        styles.item,
        {
          borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
          backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
        },
      ]}
      accessibilityLabel={`Classe ${item.nom_classe}`}
    >
      <View style={{ flex: 1 }}>
        <Text style={[styles.itemTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{item.nom_classe}</Text>
        <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
          Niveau: {item.niveau} • Année: {item.annee_scolaire}
        </Text>
      </View>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <TouchableOpacity
          onPress={() => router.push({ pathname: '/app/admin/classes/[id]/edit', params: { id: encodeURIComponent(item.nom_classe) } } as any)}
          style={[styles.smallBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
        >
          <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>Éditer</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => confirmDelete(item.nom_classe)}
          style={[styles.smallBtn, { borderColor: 'rgba(239,68,68,0.35)' }]}
        >
          <Text style={{ fontWeight: '800', color: '#ef4444' }}>Supprimer</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text>
        <TouchableOpacity onPress={load} style={styles.retryBtn}>
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={{ flex: 1, padding: 16 }}>
        <View style={[styles.headerRow, { justifyContent: 'space-between' }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Classes</Text>
          </View>
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/app/admin/classes/create' } as any)}
            style={[styles.createBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            accessibilityRole="button"
            accessibilityLabel="Ajouter une classe"
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Ajouter</Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.searchWrap, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
          <TextInput
            placeholder="Rechercher..."
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            value={q}
            onChangeText={setQ}
            style={{ padding: 10, color: isDark ? '#F8FAFC' : '#0F172A' }}
          />
        </View>

        <FlatList
          data={filtered}
          keyExtractor={(item, index) => item?.nom_classe ? item.nom_classe : String(index)}
          renderItem={renderItem}
          contentContainerStyle={filtered.length === 0 ? { flexGrow: 1 } : undefined}
          ListEmptyComponent={
            <View style={styles.center}>
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucune classe trouvée</Text>
            </View>
          }
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  item: { padding: 14, borderRadius: 16, borderWidth: 1, marginBottom: 10, flexDirection: 'row', alignItems: 'center', gap: 10 },
  itemTitle: { fontSize: 16, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 4 },
  smallBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  searchWrap: { borderWidth: 1, borderRadius: 12, marginBottom: 12 },
});
import React, { useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, useColorScheme, Alert, TextInput, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import type { UpdateClassDto, Class } from '../../../../../src/types/classes';
import { classesService } from '../../../../../src/services/classes';

export default function EditClassScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const oldNomClasse = useMemo(() => {
    const id = Array.isArray(params.id) ? params.id[0] : params.id;
    return decodeURIComponent(id || '');
  }, [params.id]);

  const [form, setForm] = useState<UpdateClassDto>({
    nom_classe: '',
    niveau: '',
    annee_scolaire: '',
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  React.useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        // Try to get list and locate item
        const list = await classesService.list();
        const item = (list || []).find((c: Class) => c.nom_classe === oldNomClasse);
        if (item && mounted) {
          setForm({
            nom_classe: item.nom_classe,
            niveau: item.niveau,
            annee_scolaire: item.annee_scolaire,
          });
        }
      } catch (e) {
        // ignore, handled by form defaults
      } finally {
        if (mounted) setLoading(false);
      }
    })();
    return () => { mounted = false; };
  }, [oldNomClasse]);

  const setField = (k: keyof UpdateClassDto, v: string) => setForm(prev => ({ ...prev, [k]: v }));

  const validate = (): string | null => {
    if (!form.nom_classe.trim() || !form.niveau.trim() || !form.annee_scolaire.trim()) {
      return 'Tous les champs sont requis.';
    }
    return null;
  };

  const onSubmit = async () => {
    const validation = validate();
    if (validation) {
      Alert.alert('Validation', validation);
      return;
    }
    setSubmitting(true);
    try {
      await classesService.update(oldNomClasse, {
        nom_classe: form.nom_classe.trim(),
        niveau: form.niveau.trim(),
        annee_scolaire: form.annee_scolaire.trim(),
      });
      Alert.alert('Succès', 'Classe mise à jour avec succès');
      router.replace('/app/admin/classes');
    } catch (e: any) {
      const msg = e?.response?.data?.message || e?.message || 'Erreur lors de la mise à jour';
      Alert.alert('Erreur', msg);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.select({ ios: 'padding', android: undefined })}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={{ flex: 1, padding: 16 }}>
            <View style={[styles.headerRow, { justifyContent: 'space-between' }]}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <TouchableOpacity
                  onPress={() => router.back()}
                  accessibilityRole="button"
                  accessibilityLabel="Retour"
                  style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
                >
                  <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
                </TouchableOpacity>
                <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Modifier: {oldNomClasse}</Text>
              </View>
            </View>

            <View style={{ gap: 12 }}>
              <View>
                <Text style={[styles.label, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Nom de la classe</Text>
                <TextInput
                  value={form.nom_classe}
                  onChangeText={(t) => setField('nom_classe', t)}
                  placeholder="e.g., 6ème A"
                  placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
                  style={[styles.input, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)', color: isDark ? '#F8FAFC' : '#0F172A' }]}
                  editable={!submitting}
                />
              </View>

              <View>
                <Text style={[styles.label, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Niveau</Text>
                <TextInput
                  value={form.niveau}
                  onChangeText={(t) => setField('niveau', t)}
                  placeholder="e.g., 6ème"
                  placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
                  style={[styles.input, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)', color: isDark ? '#F8FAFC' : '#0F172A' }]}
                  editable={!submitting}
                />
              </View>

              <View>
                <Text style={[styles.label, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Année scolaire</Text>
                <TextInput
                  value={form.annee_scolaire}
                  onChangeText={(t) => setField('annee_scolaire', t)}
                  placeholder="e.g., 2024-2025"
                  placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
                  style={[styles.input, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)', color: isDark ? '#F8FAFC' : '#0F172A' }]}
                  editable={!submitting}
                />
              </View>
            </View>

            <TouchableOpacity
              disabled={submitting}
              onPress={onSubmit}
              style={[styles.submitBtn, { opacity: submitting ? 0.7 : 1 }]}
              accessibilityRole="button"
              accessibilityLabel="Enregistrer la classe"
            >
              {submitting ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={{ color: '#fff', fontWeight: '800' }}>Enregistrer</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  label: { fontSize: 14, fontWeight: '700', marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  submitBtn: { marginTop: 16, backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 12, borderRadius: 12, alignItems: 'center' },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
});
import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, ScrollView, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useCourses, useUpdateCourse } from '../../../../../src/hooks/useCourses';
import { classesService } from '../../../../../src/services/classes';
import { coursesService } from '../../../../../src/services/courses';
import type { Class } from '../../../../../src/types/classes';
import type { TeacherOption } from '../../../../../src/types/courses';
import Select from '../../../../../src/components/Select';

export default function EditCourseScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ id?: string }>();
  const id = useMemo(() => {
    const raw = params?.id ?? '';
    const n = Number(raw);
    return Number.isFinite(n) ? n : NaN;
  }, [params]);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { data: courses } = useCourses();
  const { mutate: updateCourse, loading: saving } = useUpdateCourse();

  const current = useMemo(() => {
    if (!Array.isArray(courses)) return undefined;
    return courses.find(c => c.matiere_id === id);
  }, [courses, id]);

  const [nom, setNom] = useState('');
  const [nomClasse, setNomClasse] = useState('');
  const [profMatricule, setProfMatricule] = useState<string | null>(null);

  const [classOptions, setClassOptions] = useState<Class[]>([]);
  const [teacherOptions, setTeacherOptions] = useState<TeacherOption[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (current) {
      setNom(current.nom ?? '');
      setNomClasse(current.nom_classe ?? '');
      setProfMatricule(current.professeur_matricule ?? null);
    }
  }, [current]);

  const loadOptions = useCallback(async () => {
    setError(null);
    setLoadingOptions(true);
    try {
      const [classes, teachers] = await Promise.all([
        classesService.list().catch(() => []),
        coursesService.listTeachers().catch(() => []),
      ]);
      setClassOptions(Array.isArray(classes) ? classes : []);
      setTeacherOptions(Array.isArray(teachers) ? teachers : []);
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors du chargement des options';
      setError(msg);
    } finally {
      setLoadingOptions(false);
    }
  }, []);

  useEffect(() => {
    loadOptions();
  }, [loadOptions]);

  const canSubmit = useMemo(() => {
    return Number.isFinite(id) && nom.trim().length > 0 && nomClasse.trim().length > 0 && !saving;
  }, [id, nom, nomClasse, saving]);

  const onSubmit = async () => {
    if (!canSubmit || !Number.isFinite(id)) return;
    try {
      await updateCourse(id as number, {
        nom: nom.trim(),
        nom_classe: nomClasse.trim(),
        professeur_matricule: profMatricule || null,
      });
      Alert.alert('Succès', 'Cours mis à jour avec succès');
      router.back();
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors de la mise à jour du cours';
      Alert.alert('Erreur', msg);
    }
  };

  if (!Number.isFinite(id)) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444' }}>Identifiant de cours invalide</Text>
      </View>
    );
  }

  if (!current) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ marginTop: 8, color: isDark ? '#CBD5E1' : '#334155' }}>Chargement du cours...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <View style={[styles.headerRow, { justifyContent: 'space-between' }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Éditer le cours</Text>
          </View>
          <TouchableOpacity
            onPress={onSubmit}
            disabled={!canSubmit}
            style={[
              styles.createBtn,
              {
                opacity: canSubmit ? 1 : 0.6,
                borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)',
              },
            ]}
            accessibilityRole="button"
            accessibilityLabel="Enregistrer"
          >
            {saving ? (
              <ActivityIndicator />
            ) : (
              <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Enregistrer</Text>
            )}
          </TouchableOpacity>
        </View>

        {error ? (
          <View style={[styles.alert, { borderColor: '#ef4444' }]}>
            <Text style={{ color: '#ef4444' }}>{error}</Text>
          </View>
        ) : null}

        {/* Nom */}
        <View style={styles.field}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom du cours</Text>
          <TextInput
            value={nom}
            onChangeText={setNom}
            placeholder="Ex: Mathématiques"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            style={[
              styles.input,
              {
                color: isDark ? '#F8FAFC' : '#0F172A',
                borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
              },
            ]}
          />
        </View>

        {/* Classe select (shared Select) */}
        <View style={styles.field}>
          <Select
            label="Classe"
            placeholder="Sélectionner une classe"
            value={nomClasse || undefined}
            onValueChange={setNomClasse}
            options={classOptions.map(c => ({ label: c.nom_classe, value: c.nom_classe }))}
            disabled={loadingOptions}
          />
          {loadingOptions ? (
            <View style={{ marginTop: 6, flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <ActivityIndicator />
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Chargement des classes...</Text>
            </View>
          ) : null}
        </View>

        {/* Professeur select (optional via shared Select) */}
        <View style={styles.field}>
          <Select
            label="Professeur (optionnel)"
            placeholder="Sélectionner un professeur"
            value={profMatricule ?? undefined}
            onValueChange={(v) => setProfMatricule(v === '__NONE__' ? null : v)}
            options={[
              { label: '— Aucun —', value: '__NONE__' },
              ...teacherOptions.map(t => ({ label: `${t.prenom} ${t.nom}`.trim(), value: t.matricule }))
            ]}
            disabled={loadingOptions}
          />
          {loadingOptions ? (
            <View style={{ marginTop: 6, flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <ActivityIndicator />
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Chargement des professeurs...</Text>
            </View>
          ) : null}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, marginBottom: 12 },
  field: { marginBottom: 12 },
  label: { fontWeight: '800', marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, padding: 10 },
  optionRow: { paddingVertical: 10, paddingHorizontal: 12 },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
});
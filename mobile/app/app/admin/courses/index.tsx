import React, { useMemo, useState, useCallback, useEffect, memo } from 'react';
import { View, FlatList, RefreshControl, Alert, TouchableOpacity, Text, TextInput, ActivityIndicator, useColorScheme, StyleSheet, Modal, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useCourses, useDeleteCourse } from '../../../../src/hooks/useCourses';
import type { Course } from '../../../../src/types/courses';

function getTeacherFullName(item: Course): string | null {
  const { professeur_nom, professeur_prenom } = item;
  if (professeur_nom || professeur_prenom) {
    return [professeur_prenom, professeur_nom].filter(Boolean).join(' ');
  }
  return null;
}

function getTeacherShortName(item: Course): string | null {
  const { professeur_nom, professeur_prenom } = item;
  if (!professeur_nom && !professeur_prenom) return null;
  const initial = professeur_prenom ? `${professeur_prenom[0].toUpperCase()}. ` : '';
  return `${initial}${professeur_nom ?? ''}`.trim();
}

function CourseRow({
  item,
  onEdit,
  onLongPress,
  isDark,
}: {
  item: Course;
  onEdit: (id: number) => void;
  onLongPress: (id: number) => void;
  isDark: boolean;
}) {
  const teacherShort = getTeacherShortName(item);
  return (
    <Pressable
      onPress={() => onEdit(item.matiere_id)}
      onLongPress={() => onLongPress(item.matiere_id)}
      style={({ pressed }) => [
        styles.item,
        {
          borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
          backgroundColor: isDark ? (pressed ? 'rgba(255,255,255,0.06)' : 'rgba(255,255,255,0.04)') : (pressed ? 'rgba(15,23,42,0.04)' : 'rgba(15,23,42,0.02)'),
        },
      ]}
      accessibilityLabel={`Cours ${item.nom}`}
      accessibilityHint="Appuyer pour éditer, appui long pour plus d'actions"
    >
      <View style={{ flex: 1, paddingRight: 8 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
          <Text style={[styles.itemTitle, { color: isDark ? '#F8FAFC' : '#0F172A', flex: 1 }]} numberOfLines={1}>{item.nom}</Text>
          {teacherShort ? (
            <View style={[styles.chip, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)', backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(15,23,42,0.03)' }]}>
              <Text style={{ fontSize: 12, color: isDark ? '#E2E8F0' : '#0F172A' }} numberOfLines={1}>{teacherShort}</Text>
            </View>
          ) : null}
          <Text style={{ color: isDark ? '#94A3B8' : '#64748B', marginLeft: 6 }}>›</Text>
        </View>
        <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]} numberOfLines={1}>{item.nom_classe}</Text>
      </View>
    </Pressable>
  );
}

// Memoized search header component to keep stable identity and preserve focus
const SearchHeader = memo(function SearchHeader({
  value,
  onChange,
  onClear,
  isDark,
  onBack,
  onCreate,
}: {
  value: string;
  onChange: (t: string) => void;
  onClear: () => void;
  isDark: boolean;
  onBack: () => void;
  onCreate: () => void;
}) {
  return (
    <View style={{ paddingHorizontal: 16, paddingBottom: 12, paddingTop: 8, backgroundColor: isDark ? '#0B1220' : '#FFFFFF' }}>
      <View style={[styles.headerRow, { justifyContent: 'space-between', marginBottom: 12 }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <TouchableOpacity
            onPress={onBack}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Cours</Text>
        </View>
        <TouchableOpacity
          onPress={onCreate}
          style={[styles.createBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          accessibilityRole="button"
          accessibilityLabel="Ajouter un cours"
        >
          <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Ajouter</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.searchWrap, { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 8, borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
        <TextInput
          placeholder="Rechercher par nom ou classe"
          placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
          value={value}
          onChangeText={onChange}
          style={{ paddingVertical: 10, paddingHorizontal: 6, color: isDark ? '#F8FAFC' : '#0F172A', flex: 1 }}
          blurOnSubmit={false}
          returnKeyType="search"
        />
        {value.length > 0 ? (
          <Pressable onPress={onClear} hitSlop={8} style={{ padding: 6 }} accessibilityLabel="Effacer la recherche">
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>✕</Text>
          </Pressable>
        ) : null}
      </View>
      <Text style={{ fontSize: 12, color: isDark ? '#64748B' : '#94A3B8', marginTop: 6 }}>Tirez pour actualiser la liste</Text>
    </View>
  );
});

export default function CoursesIndexScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { data, loading, error, refetch } = useCourses();
  const { mutate: deleteCourse, loading: deleting } = useDeleteCourse();

  // search and debounce
  const [searchText, setSearchText] = useState('');
  const [query, setQuery] = useState('');
  // Stable callbacks to avoid changing props identity
  const handleChange = useCallback((t: string) => setSearchText(t), []);
  const handleClear = useCallback(() => setSearchText(''), []);
  const handleBack = useCallback(() => router.back(), [router]);
  const handleCreate = useCallback(() => {
    router.push({ pathname: '/app/admin/courses/create' } as any);
  }, [router]);

  // debounce without remounts
  useEffect(() => {
    const t = setTimeout(() => setQuery(searchText), 250);
    return () => clearTimeout(t);
  }, [searchText]);

  // action sheet modal state
  const [actionId, setActionId] = useState<number | null>(null);
  const [actionVisible, setActionVisible] = useState(false);

  const filtered = useMemo(() => {
    if (!Array.isArray(data)) return [];
    const q = query.trim().toLowerCase();
    if (!q) return data;
    return data.filter((c) => {
      return (
        (c.nom || '').toLowerCase().includes(q) ||
        (c.nom_classe || '').toLowerCase().includes(q)
      );
    });
  }, [data, query]);

  const onConfirmDelete = useCallback(
    (id: number) => {
      Alert.alert(
        'Confirmer la suppression',
        'Voulez-vous vraiment supprimer ce cours ?',
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Supprimer',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteCourse(id);
                await refetch();
              } catch {
                // handled by hook
              }
            },
          },
        ]
      );
    },
    [deleteCourse, refetch]
  );

  const onEdit = useCallback((id: number) => {
    router.push({ pathname: '/app/admin/courses/[id]/edit', params: { id: String(id) } } as any);
  }, [router]);

  const onLongPressRow = useCallback((id: number) => {
    setActionId(id);
    setActionVisible(true);
  }, []);

  const closeActions = useCallback(() => {
    setActionVisible(false);
    // small delay to avoid acting on closing animation
    setTimeout(() => setActionId(null), 200);
  }, []);

  // Stable ListHeaderComponent using useMemo to prevent remounts
  const Header = useMemo(() => (
    <SearchHeader
      value={searchText}
      onChange={handleChange}
      onClear={handleClear}
      isDark={isDark}
      onBack={handleBack}
      onCreate={handleCreate}
    />
  ), [searchText, handleChange, handleClear, isDark, handleBack, handleCreate]);

  const showErrorBanner = !!error;
  const ErrorBanner = showErrorBanner ? (
    <View style={{ paddingHorizontal: 16, paddingVertical: 10, backgroundColor: 'rgba(239,68,68,0.08)', borderColor: 'rgba(239,68,68,0.35)', borderWidth: 1, margin: 16, borderRadius: 10 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
        <Text style={{ color: '#ef4444', flex: 1 }} numberOfLines={2}>{String(error)}</Text>
        <TouchableOpacity onPress={() => refetch()} style={[styles.retryBtn, { backgroundColor: '#ef4444' }]} accessibilityLabel="Réessayer">
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    </View>
  ) : null;

  const emptyComponent = (
    <View style={[styles.center, { paddingHorizontal: 16 }]}>
      <Text style={{ fontSize: 48, marginBottom: 8 }} accessible accessibilityLabel="Aucun cours">📚</Text>
      <Text style={{ color: isDark ? '#94A3B8' : '#64748B', marginBottom: 12 }}>Aucun cours trouvé</Text>
      <TouchableOpacity onPress={handleCreate} style={[styles.retryBtn, { backgroundColor: '#2563EB' }]} accessibilityLabel="Ajouter un cours">
        <Text style={{ color: '#fff', fontWeight: '800' }}>Ajouter un cours</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading && (!data || data.length === 0)) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {ErrorBanner}
      <FlatList
        data={filtered}
        keyExtractor={(item) => String(item.matiere_id)}
        renderItem={({ item }) => (
          <CourseRow item={item} onEdit={onEdit} onLongPress={onLongPressRow} isDark={isDark} />
        )}
        ListHeaderComponent={Header}
        stickyHeaderIndices={[0]}
        refreshControl={
          <RefreshControl refreshing={loading || deleting} onRefresh={() => refetch()} />
        }
        contentContainerStyle={filtered.length === 0 ? { flexGrow: 1, justifyContent: 'center' } : { paddingHorizontal: 16, paddingTop: 8, paddingBottom: 24 }}
        ListEmptyComponent={emptyComponent}
        keyboardShouldPersistTaps="handled"
      />

      <Modal
        visible={actionVisible}
        transparent
        animationType="slide"
        onRequestClose={closeActions}
      >
        <Pressable style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.3)' }} onPress={closeActions} />
        <View style={[styles.sheet, { backgroundColor: isDark ? '#0B1220' : '#FFFFFF', borderTopColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}>
          <View style={{ padding: 16 }}>
            <Text style={{ fontWeight: '800', fontSize: 16, color: isDark ? '#F8FAFC' : '#0F172A', marginBottom: 12 }}>Actions</Text>
            <Pressable
              onPress={() => {
                const id = actionId;
                closeActions();
                if (id != null) onEdit(id);
              }}
              style={({ pressed }) => [styles.actionBtn, { backgroundColor: pressed ? (isDark ? 'rgba(255,255,255,0.06)' : 'rgba(15,23,42,0.04)') : 'transparent', borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}
              accessibilityLabel="Éditer"
            >
              <Text style={{ fontWeight: '700', color: isDark ? '#E2E8F0' : '#0F172A' }}>Éditer</Text>
            </Pressable>
            <Pressable
              onPress={() => {
                const id = actionId;
                closeActions();
                if (id != null) onConfirmDelete(id);
              }}
              style={({ pressed }) => [styles.actionBtn, { backgroundColor: pressed ? 'rgba(239,68,68,0.08)' : 'transparent', borderColor: 'rgba(239,68,68,0.35)' }]}
              accessibilityLabel="Supprimer"
            >
              <Text style={{ fontWeight: '700', color: '#ef4444' }}>Supprimer</Text>
            </Pressable>
            <Pressable onPress={closeActions} style={[styles.actionBtn, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]} accessibilityLabel="Fermer">
              <Text style={{ fontWeight: '700', color: isDark ? '#94A3B8' : '#64748B' }}>Fermer</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  title: { fontSize: 20, fontWeight: '800' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  item: { padding: 14, borderRadius: 16, borderWidth: 1, marginBottom: 10, flexDirection: 'row', alignItems: 'center', gap: 10 },
  itemTitle: { fontSize: 16, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 4 },
  smallBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  searchWrap: { borderWidth: 1, borderRadius: 12, marginBottom: 0 },
  chip: { paddingVertical: 4, paddingHorizontal: 8, borderWidth: 1, borderRadius: 999 },
  sheet: { borderTopWidth: 1, borderTopLeftRadius: 16, borderTopRightRadius: 16, position: 'absolute', left: 0, right: 0, bottom: 0 },
  actionBtn: { paddingVertical: 12, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, marginBottom: 10 },
});
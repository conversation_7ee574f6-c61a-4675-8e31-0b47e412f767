import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, ScrollView, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useCreateCourse } from '../../../../src/hooks/useCourses';
import { coursesService } from '../../../../src/services/courses';
import { classesService } from '../../../../src/services/classes';
import type { TeacherOption } from '../../../../src/types/courses';
import type { Class } from '../../../../src/types/classes';
import Select from '../../../../src/components/Select';

export default function CreateCourseScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { mutate: createCourse, loading: saving } = useCreateCourse();

  const [nom, setNom] = useState('');
  const [nomClasse, setNomClasse] = useState('');
  const [profMatricule, setProfMatricule] = useState<string | null>(null);

  const [classOptions, setClassOptions] = useState<Class[]>([]);
  const [teacherOptions, setTeacherOptions] = useState<TeacherOption[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadOptions = useCallback(async () => {
    setError(null);
    setLoadingOptions(true);
    try {
      const [classes, teachers] = await Promise.all([
        classesService.list().catch(() => []),
        coursesService.listTeachers().catch(() => []),
      ]);
      setClassOptions(Array.isArray(classes) ? classes : []);
      setTeacherOptions(Array.isArray(teachers) ? teachers : []);
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors du chargement des options';
      setError(msg);
    } finally {
      setLoadingOptions(false);
    }
  }, []);

  useEffect(() => {
    loadOptions();
  }, [loadOptions]);

  const canSubmit = useMemo(() => {
    return nom.trim().length > 0 && nomClasse.trim().length > 0 && !saving;
  }, [nom, nomClasse, saving]);

  const onSubmit = async () => {
    if (!canSubmit) return;
    try {
      await createCourse({
        nom: nom.trim(),
        nom_classe: nomClasse.trim(),
        professeur_matricule: profMatricule || null,
      });
      Alert.alert('Succès', 'Cours créé avec succès');
      router.back();
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors de la création du cours';
      Alert.alert('Erreur', msg);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <View style={[styles.headerRow, { justifyContent: 'space-between' }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Créer un cours</Text>
          </View>
          <TouchableOpacity
            onPress={onSubmit}
            disabled={!canSubmit}
            style={[
              styles.createBtn,
              {
                opacity: canSubmit ? 1 : 0.6,
                borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)',
              },
            ]}
            accessibilityRole="button"
            accessibilityLabel="Enregistrer le cours"
          >
            {saving ? (
              <ActivityIndicator />
            ) : (
              <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Enregistrer</Text>
            )}
          </TouchableOpacity>
        </View>

        {error ? (
          <View style={[styles.alert, { borderColor: '#ef4444' }]}>
            <Text style={{ color: '#ef4444' }}>{error}</Text>
          </View>
        ) : null}

        {/* Nom */}
        <View style={styles.field}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom du cours</Text>
          <TextInput
            value={nom}
            onChangeText={setNom}
            placeholder="Ex: Mathématiques"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            style={[
              styles.input,
              {
                color: isDark ? '#F8FAFC' : '#0F172A',
                borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
              },
            ]}
          />
        </View>

        {/* Classe select (shared Select) */}
        <View style={styles.field}>
          <Select
            label="Classe"
            placeholder="Sélectionner une classe"
            value={nomClasse || undefined}
            onValueChange={setNomClasse}
            options={classOptions.map(c => ({ label: c.nom_classe, value: c.nom_classe }))}
            disabled={loadingOptions}
          />
          {loadingOptions ? (
            <View style={{ marginTop: 6, flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <ActivityIndicator />
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Chargement des classes...</Text>
            </View>
          ) : undefined}
        </View>

        {/* Professeur select (optional via shared Select) */}
        <View style={styles.field}>
          <Select
            label="Professeur (optionnel)"
            placeholder="Sélectionner un professeur"
            value={profMatricule ?? undefined}
            onValueChange={(v) => setProfMatricule(v === '__NONE__' ? null : v)}
            options={[
              { label: '— Aucun —', value: '__NONE__' },
              ...teacherOptions.map(t => ({ label: `${t.prenom} ${t.nom}`.trim(), value: t.matricule }))
            ]}
            disabled={loadingOptions}
          />
          {loadingOptions ? (
            <View style={{ marginTop: 6, flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <ActivityIndicator />
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Chargement des professeurs...</Text>
            </View>
          ) : undefined}
        </View>

        <TouchableOpacity
          onPress={onSubmit}
          disabled={!canSubmit}
          style={[
            styles.submitBtn,
            { backgroundColor: canSubmit ? '#2563EB' : '#93C5FD' },
          ]}
          accessibilityRole="button"
          accessibilityLabel="Créer le cours"
        >
          {saving ? <ActivityIndicator color="#fff" /> : <Text style={{ color: '#fff', fontWeight: '800' }}>Créer</Text>}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, marginBottom: 12 },
  field: { marginBottom: 12 },
  label: { fontWeight: '800', marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, padding: 10 },
  optionRow: { paddingVertical: 10, paddingHorizontal: 12 },
  submitBtn: { marginTop: 8, paddingVertical: 12, alignItems: 'center', borderRadius: 12 },
});
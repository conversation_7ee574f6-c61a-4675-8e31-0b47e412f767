import { Stack } from 'expo-router';

export default function MessagesLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: 'Messages',
        }}
      />
      <Stack.Screen 
        name="compose" 
        options={{
          title: 'Nouveau message',
          presentation: 'modal',
        }}
      />
      <Stack.Screen 
        name="conversation" 
        options={{
          title: 'Conversation',
        }}
      />
    </Stack>
  );
}

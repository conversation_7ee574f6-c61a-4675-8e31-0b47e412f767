import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  useColorScheme,
  FlatList,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { messageService } from '../../../../src/services/messageService';
import type { Conversation } from '../../../../src/types/api';

export default function AdminMessagesScreen() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [query, setQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'annonce' | 'prive'>('all');

  const router = useRouter();
  const isDark = useColorScheme() === 'dark';

  const loadData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const conversationsData = await messageService.getAdminConversations();
      setConversations(conversationsData);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement des messages');
    } finally {
      setLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      const conversationsData = await messageService.getAdminConversations();
      setConversations(conversationsData);
    } catch (e: any) {
      // keep previous error state minimal during pull refresh
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Refresh list when screen gains focus
  useFocusEffect(
    React.useCallback(() => {
      loadData();
      return () => {};
    }, [loadData])
  );


  const handleComposePress = () => {
    router.push('/app/admin/messages/compose');
  };

  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    return conversations.filter((c) => {
      const matchesType =
        filter === 'all' ||
        (filter === 'annonce' && c.type === 'annonce') ||
        (filter === 'prive' && c.type !== 'annonce');
      if (!matchesType) return false;
      if (!q) return true;
      const name = (c.participant_name || '').toLowerCase();
      const last = (c.last_message || '').toLowerCase();
      return name.includes(q) || last.includes(q);
    });
  }, [conversations, query, filter]);

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>
          Chargement des messages...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text>
        <TouchableOpacity onPress={loadData} style={styles.retryBtn}>
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }}>
      <View style={[styles.header, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
          >
            <Ionicons name="arrow-back" size={22} color={isDark ? '#E2E8F0' : '#0F172A'} />
          </TouchableOpacity>

          <Text style={[styles.headerTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>
            Messagerie
          </Text>

          <View style={{ width: 40 }} />
        </View>

        <View style={styles.searchRow}>
          <View
            style={[
              styles.searchBox,
              {
                backgroundColor: isDark ? 'rgba(255,255,255,0.06)' : '#FFFFFF',
                borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.08)',
              },
            ]}
          >
            <Ionicons name="search" size={18} color={isDark ? '#94A3B8' : '#64748B'} />
            <TextInput
              placeholder="Rechercher..."
              placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
              style={[styles.searchInput, { color: isDark ? '#F8FAFC' : '#0F172A' }]}
              value={query}
              onChangeText={setQuery}
              returnKeyType="search"
            />
            {query.length > 0 && (
              <TouchableOpacity onPress={() => setQuery('')} style={styles.clearBtn} hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}>
                <Ionicons name="close-circle" size={18} color={isDark ? '#94A3B8' : '#94A3B8'} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.filterRow}>
            <TouchableOpacity
              onPress={() => setFilter('all')}
              style={[
                styles.chip,
                {
                  backgroundColor: filter === 'all' ? (isDark ? '#1F2937' : '#E5E7EB') : 'transparent',
                  borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.1)',
                },
              ]}
            >
              <Text style={[styles.chipText, { color: isDark ? '#E5E7EB' : '#111827' }]}>Tous</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setFilter('annonce')}
              style={[
                styles.chip,
                {
                  backgroundColor: filter === 'annonce' ? (isDark ? '#1F2937' : '#E5E7EB') : 'transparent',
                  borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.1)',
                },
              ]}
            >
              <Text style={[styles.chipText, { color: isDark ? '#E5E7EB' : '#111827' }]}>Annonces</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setFilter('prive')}
              style={[
                styles.chip,
                {
                  backgroundColor: filter === 'prive' ? (isDark ? '#1F2937' : '#E5E7EB') : 'transparent',
                  borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.1)',
                },
              ]}
            >
              <Text style={[styles.chipText, { color: isDark ? '#E5E7EB' : '#111827' }]}>Privés</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {filtered.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="chatbubbles-outline" size={64} color={isDark ? '#475569' : '#94A3B8'} />
          <Text style={[styles.emptyText, { color: isDark ? '#94A3B8' : '#64748B' }]}>
            {query || filter !== 'all' ? 'Aucun résultat' : 'Messagerie Admin'}
          </Text>
          <Text style={[styles.emptySubtext, { color: isDark ? '#64748B' : '#94A3B8' }]}>
            {query || filter !== 'all'
              ? 'Essayez d’ajuster votre recherche ou vos filtres'
              : 'Système de messagerie pour administrateurs'}
          </Text>
          <TouchableOpacity style={styles.testButton} onPress={handleComposePress}>
            <Text style={styles.testButtonText}>Composer un message</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filtered}
          keyExtractor={(conv) => `${conv.participant_id}-${conv.type}`}
          contentContainerStyle={styles.listContent}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={isDark ? '#E5E7EB' : '#334155'} />}
          renderItem={({ item: conv }) => (
            <TouchableOpacity
              style={[
                styles.conversationItem,
                {
                  borderColor: isDark ? 'rgba(148,163,184,0.15)' : 'rgba(15,23,42,0.08)',
                  backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : '#FFFFFF',
                },
              ]}
              onPress={() => {
                router.push(`/app/admin/messages/${conv.participant_id}`);
              }}
              activeOpacity={0.7}
            >
              <View style={styles.conversationHeader}>
                <View style={styles.participantInfo}>
                  <View
                    style={[
                      styles.avatar,
                      { backgroundColor: conv.type === 'annonce' ? '#10B981' : '#3B82F6' },
                    ]}
                  >
                    <Text style={styles.avatarText}>
                      {(conv.participant_name || 'N')[0]}
                    </Text>
                  </View>
                  <View style={styles.participantDetails}>
                    <Text
                      style={[
                        styles.participantName,
                        { color: isDark ? '#F8FAFC' : '#0F172A' },
                      ]}
                      numberOfLines={1}
                    >
                      {conv.participant_name || 'Inconnu'}
                    </Text>
                    <Text
                      style={[
                        styles.participantRole,
                        { color: isDark ? '#94A3B8' : '#64748B' },
                      ]}
                      numberOfLines={1}
                    >
                      {conv.type === 'annonce' ? 'Annonce' : 'Message privé'}
                    </Text>
                  </View>
                </View>
                <View style={styles.conversationMeta}>
                  {conv.unread_count > 0 && (
                    <View style={styles.unreadBadge}>
                      <Text style={styles.unreadText}>{conv.unread_count}</Text>
                    </View>
                  )}
                </View>
              </View>

              {!!conv.last_message && (
                <Text
                  style={[
                    styles.lastMessage,
                    { color: isDark ? '#CBD5E1' : '#334155' },
                  ]}
                  numberOfLines={2}
                >
                  {conv.last_message}
                </Text>
              )}
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
        />
      )}

      <TouchableOpacity
        style={styles.fab}
        onPress={handleComposePress}
        accessibilityRole="button"
        accessibilityLabel="Composer"
        activeOpacity={0.8}
      >
        <Ionicons name="create" size={22} color="#FFFFFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  retryBtn: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(148,163,184,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 24,
    backgroundColor: '#3B82F6',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    gap: 12,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  statTitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 80,
  },
  conversationItem: {
    borderWidth: 1,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  participantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  participantDetails: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  participantRole: {
    fontSize: 13,
    fontWeight: '500',
  },
  conversationMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  messageTime: {
    fontSize: 12,
    fontWeight: '500',
  },
  unreadBadge: {
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  lastMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 6,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 64,
    gap: 12,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  testButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  testButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '600',
  },
  searchRow: {
    marginTop: 8,
    gap: 10,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 4,
  },
  clearBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterRow: {
    flexDirection: 'row',
    gap: 8,
  },
  chip: {
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

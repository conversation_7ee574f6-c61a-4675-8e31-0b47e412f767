import React, { useCallback, useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  useColorScheme,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { messageService } from '../../../../src/services/messageService';
import type { Message } from '../../../../src/types/api';

export default function ConversationScreen() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const flatListRef = useRef<FlatList>(null);
  const router = useRouter();
  const params = useLocalSearchParams();
  const isDark = useColorScheme() === 'dark';

  const participantId = params.participantId as string;
  const participantName = params.participantName as string;
  const participantRole = params.participantRole as string;

  const loadMessages = useCallback(async () => {
    if (!participantId) return;
    
    setError(null);
    setLoading(true);
    try {
      const messagesData = await messageService.getConversationMessages(participantId);
      setMessages(messagesData);
      
      // Mark messages as read
      await messageService.markMessagesAsRead(participantId);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement des messages');
    } finally {
      setLoading(false);
    }
  }, [participantId]);

  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageText = newMessage.trim();
    setNewMessage('');
    setSending(true);

    try {
      await messageService.sendQuickReply(participantId, messageText);
      
      // Add the message optimistically to the UI
      const optimisticMessage: Message = {
        message_id: Date.now(),
        sender_matricule: 'current_user', // This would be the current admin's matricule
        receiver_matricule: participantId,
        contenu: messageText,
        type: 'privé',
        date_envoi: new Date().toISOString(),
        lu: true,
        sender_name: 'Vous',
        sender_role: 'admin',
      };
      
      setMessages(prev => [...prev, optimisticMessage]);
      
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
      
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Erreur', 'Impossible d\'envoyer le message');
      setNewMessage(messageText); // Restore the message text
    } finally {
      setSending(false);
    }
  };

  const renderMessage = ({ item, index }: { item: Message; index: number }) => {
    const isFromCurrentUser = item.sender_name === 'Vous' || item.sender_matricule === 'current_user';
    const showDate = index === 0 || !isSameDay(item.date_envoi, messages[index - 1]?.date_envoi);
    
    return (
      <View>
        {showDate && (
          <View style={styles.dateContainer}>
            <Text style={[styles.dateText, { color: isDark ? '#64748B' : '#94A3B8' }]}>
              {formatMessageDate(item.date_envoi)}
            </Text>
          </View>
        )}
        <View style={[
          styles.messageContainer,
          isFromCurrentUser ? styles.messageContainerSent : styles.messageContainerReceived
        ]}>
          <View style={[
            styles.messageBubble,
            isFromCurrentUser 
              ? { backgroundColor: '#3B82F6' }
              : { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                  borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                  borderWidth: 1,
                }
          ]}>
            {!isFromCurrentUser && (
              <Text style={[styles.senderName, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                {item.sender_name || participantName}
              </Text>
            )}
            <Text style={[
              styles.messageText,
              { color: isFromCurrentUser ? 'white' : (isDark ? '#F8FAFC' : '#0F172A') }
            ]}>
              {item.contenu}
            </Text>
            <Text style={[
              styles.messageTime,
              { color: isFromCurrentUser ? 'rgba(255,255,255,0.7)' : (isDark ? '#64748B' : '#94A3B8') }
            ]}>
              {formatMessageTime(item.date_envoi)}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#F1F5F9', '#E2E8F0']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color={isDark ? '#60A5FA' : '#3B82F6'} />
          <Text style={[styles.loadingText, { color: isDark ? '#94A3B8' : '#64748B' }]}>
            Chargement de la conversation...
          </Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#F1F5F9', '#E2E8F0']}
          style={styles.errorContainer}
        >
          <Ionicons name="alert-circle" size={48} color={isDark ? '#EF4444' : '#DC2626'} />
          <Text style={[styles.errorText, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadMessages}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
      <LinearGradient
        colors={isDark ? ['#1E293B', '#0F172A'] : ['#F1F5F9', '#E2E8F0']}
        style={styles.gradient}
      >
        <BlurView intensity={20} style={styles.header}>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
            >
              <Ionicons name="arrow-back" size={24} color={isDark ? '#F8FAFC' : '#0F172A'} />
            </TouchableOpacity>
            <View style={styles.participantInfo}>
              <View style={[styles.avatar, { backgroundColor: getAvatarColor(participantRole) }]}>
                <Text style={styles.avatarText}>
                  {participantName.split(' ').map(n => n[0]).join('').toUpperCase()}
                </Text>
              </View>
              <View style={styles.participantDetails}>
                <Text style={[styles.participantName, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                  {participantName}
                </Text>
                <Text style={[styles.participantRole, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                  {participantRole === 'professeur' ? 'Professeur' : participantRole}
                </Text>
              </View>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => {/* Add call functionality if needed */}}
                accessibilityRole="button"
                accessibilityLabel="Plus d'options"
              >
                <Ionicons name="ellipsis-vertical" size={20} color={isDark ? '#F8FAFC' : '#0F172A'} />
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>

        <KeyboardAvoidingView 
          style={styles.content}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.message_id.toString()}
            contentContainerStyle={styles.messagesList}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={64} color={isDark ? '#475569' : '#94A3B8'} />
                <Text style={[styles.emptyText, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                  Aucun message
                </Text>
                <Text style={[styles.emptySubtext, { color: isDark ? '#64748B' : '#94A3B8' }]}>
                  Commencez la conversation
                </Text>
              </View>
            }
          />

          <BlurView intensity={20} style={styles.inputContainer}>
            <View style={[
              styles.inputWrapper,
              { 
                borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.8)',
              }
            ]}>
              <TextInput
                style={[styles.textInput, { color: isDark ? '#F8FAFC' : '#0F172A' }]}
                value={newMessage}
                onChangeText={setNewMessage}
                placeholder="Tapez votre message..."
                placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
                multiline
                maxLength={500}
                editable={!sending}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  { 
                    opacity: (!newMessage.trim() || sending) ? 0.5 : 1,
                    backgroundColor: '#3B82F6',
                  }
                ]}
                onPress={handleSendMessage}
                disabled={!newMessage.trim() || sending}
                accessibilityRole="button"
                accessibilityLabel="Envoyer le message"
              >
                {sending ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="send" size={20} color="white" />
                )}
              </TouchableOpacity>
            </View>
          </BlurView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const getAvatarColor = (role: string): string => {
  switch (role) {
    case 'professeur': return '#3B82F6';
    case 'admin': return '#10B981';
    case 'eleve': return '#F59E0B';
    default: return '#6B7280';
  }
};

const formatMessageTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
};

const formatMessageDate = (dateString: string): string => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (isSameDay(date, today)) {
    return 'Aujourd\'hui';
  } else if (isSameDay(date, yesterday)) {
    return 'Hier';
  } else {
    return date.toLocaleDateString('fr-FR', { 
      weekday: 'long', 
      day: 'numeric', 
      month: 'long' 
    });
  }
};

const isSameDay = (date1: string | Date, date2: string | Date): boolean => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  return d1.getDate() === d2.getDate() &&
         d1.getMonth() === d2.getMonth() &&
         d1.getFullYear() === d2.getFullYear();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(148,163,184,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  participantDetails: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  participantRole: {
    fontSize: 12,
    fontWeight: '500',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  dateContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dateText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  messageContainer: {
    marginVertical: 4,
  },
  messageContainerSent: {
    alignItems: 'flex-end',
  },
  messageContainerReceived: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 4,
  },
  messageTime: {
    fontSize: 11,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 64,
    gap: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptySubtext: {
    fontSize: 14,
  },
  inputContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(148,163,184,0.1)',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderWidth: 1,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

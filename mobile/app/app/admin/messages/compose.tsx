import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  useColorScheme,
  ActivityIndicator,
  Modal,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { messageService } from '../../../../src/services/messageService';
import type { MessageRecipient, ComposeMessageData } from '../../../../src/types/api';

export default function ComposeMessageScreen() {
  const [messageType, setMessageType] = useState<'privé' | 'annonce'>('privé');
  const [recipients, setRecipients] = useState<MessageRecipient[]>([]);
  const [selectedRecipient, setSelectedRecipient] = useState<string>('');
  const [classes, setClasses] = useState<{ nom_classe: string; niveau: string }[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [priority, setPriority] = useState<'normal' | 'urgent'>('normal');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [showRecipientModal, setShowRecipientModal] = useState(false);
  const [showClassModal, setShowClassModal] = useState(false);

  const router = useRouter();
  const isDark = useColorScheme() === 'dark';

  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [{ recipients: recipientsData, classes: classesData }] = await Promise.all([
        messageService.getMessageRecipients(),
      ]);
      setRecipients(recipientsData);
      setClasses(classesData as { nom_classe: string; niveau: string }[]);
    } catch (error) {
      console.error('Error loading compose data:', error);
      Alert.alert('Erreur', 'Impossible de charger les données');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleSend = async () => {
    if (!content.trim()) {
      Alert.alert('Erreur', 'Veuillez remplir le contenu du message');
      return;
    }

    if (messageType === 'privé' && !selectedRecipient) {
      Alert.alert('Erreur', 'Veuillez sélectionner un destinataire');
      return;
    }

    if (messageType === 'annonce' && !selectedClass) {
      Alert.alert('Erreur', 'Veuillez sélectionner une classe pour l\'annonce');
      return;
    }

    setSending(true);
    try {
      // Build payload:
      // - For private messages: no subject field is sent (match web behavior)
      // - For announcements: include subject only if provided (non-empty)
      const baseData = {
        type: messageType,
        recipients: messageType === 'privé' ? [selectedRecipient] : [],
        nom_classe: messageType === 'annonce' ? selectedClass : undefined,
        contenu: content.trim(),
        priority,
      } as ComposeMessageData;

      const messageData: ComposeMessageData =
        messageType === 'annonce' && subject.trim()
          ? { ...baseData, subject: subject.trim() }
          : baseData;

      await messageService.sendMessage(messageData);
      
      Alert.alert(
        'Succès',
        'Message envoyé avec succès',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Erreur', 'Impossible d\'envoyer le message');
    } finally {
      setSending(false);
    }
  };

  const selectRecipient = (matricule: string) => {
    // Ensure we always store a string (avoid undefined/nullable)
    setSelectedRecipient(String(matricule));
    setShowRecipientModal(false);
  };


  const renderRecipientItem = ({ item }: { item: MessageRecipient }) => {
    const isSelected = selectedRecipient === item.matricule;
    return (
      <TouchableOpacity
        style={[
          styles.recipientItem,
          {
            backgroundColor: isSelected
              ? (isDark ? '#3B82F6' : '#3B82F6')
              : (isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'),
            borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
          }
        ]}
        onPress={() => selectRecipient(item.matricule)}
      >
        <View style={styles.recipientInfo}>
          <Text style={[
            styles.recipientName,
            {
              color: isSelected ? 'white' : (isDark ? '#F8FAFC' : '#0F172A')
            }
          ]}>
            {item.prenom} {item.nom}
          </Text>
          <Text style={[
            styles.recipientMatricule,
            {
              color: isSelected ? 'rgba(255,255,255,0.8)' : (isDark ? '#94A3B8' : '#64748B')
            }
          ]}>
            {item.matricule}
          </Text>
        </View>
        {isSelected && (
          <Ionicons name="checkmark-circle" size={24} color="white" />
        )}
      </TouchableOpacity>
    );
  };
 
  const renderClassItem = ({ item }: { item: { nom_classe: string; niveau: string } }) => (
    <TouchableOpacity
      style={[
        styles.classItem,
        {
          backgroundColor: selectedClass === item.nom_classe
            ? (isDark ? '#10B981' : '#10B981')
            : (isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'),
          borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
        }
      ]}
      onPress={() => {
        setSelectedClass(item.nom_classe);
        setShowClassModal(false);
      }}
    >
      <Text style={[
        styles.className,
        { 
          color: selectedClass === item.nom_classe
            ? 'white'
            : (isDark ? '#F8FAFC' : '#0F172A')
        }
      ]}>
        {item.nom_classe}
      </Text>
      <Text style={[
        styles.classLevel,
        { 
          color: selectedClass === item.nom_classe
            ? 'rgba(255,255,255,0.8)'
            : (isDark ? '#94A3B8' : '#64748B')
        }
      ]}>
        {item.niveau}
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#F1F5F9', '#E2E8F0']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color={isDark ? '#60A5FA' : '#3B82F6'} />
          <Text style={[styles.loadingText, { color: isDark ? '#94A3B8' : '#64748B' }]}>
            Chargement...
          </Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.select({ ios: 80, android: 0 }) as number}
      >
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#F1F5F9', '#E2E8F0']}
          style={styles.gradient}
        >
          <BlurView intensity={20} style={styles.header}>
            <View style={styles.headerContent}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
                accessibilityRole="button"
                accessibilityLabel="Retour"
              >
                <Ionicons name="arrow-back" size={24} color={isDark ? '#F8FAFC' : '#0F172A'} />
              </TouchableOpacity>
              <Text style={[styles.headerTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                Nouveau message
              </Text>
              <TouchableOpacity
                style={[styles.sendButton, { opacity: sending ? 0.6 : 1 }]}
                onPress={handleSend}
                disabled={sending}
                accessibilityRole="button"
                accessibilityLabel="Envoyer le message"
              >
                {sending ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="send" size={20} color="white" />
                )}
              </TouchableOpacity>
            </View>
          </BlurView>

          <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ScrollView
              style={styles.content}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{ paddingBottom: 32 }}
            >
          {/* Message Type Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
              Type de message
            </Text>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  messageType === 'privé' && styles.typeButtonActive,
                  { borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)' }
                ]}
                onPress={() => setMessageType('privé')}
              >
                <Ionicons 
                  name="chatbubble" 
                  size={20} 
                  color={messageType === 'privé' ? 'white' : (isDark ? '#94A3B8' : '#64748B')} 
                />
                <Text style={[
                  styles.typeButtonText,
                  { color: messageType === 'privé' ? 'white' : (isDark ? '#94A3B8' : '#64748B') }
                ]}>
                  Message privé
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  messageType === 'annonce' && styles.typeButtonActive,
                  { borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)' }
                ]}
                onPress={() => setMessageType('annonce')}
              >
                <Ionicons 
                  name="megaphone" 
                  size={20} 
                  color={messageType === 'annonce' ? 'white' : (isDark ? '#94A3B8' : '#64748B')} 
                />
                <Text style={[
                  styles.typeButtonText,
                  { color: messageType === 'annonce' ? 'white' : (isDark ? '#94A3B8' : '#64748B') }
                ]}>
                  Annonce
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Recipients/Class Selection */}
          {messageType === 'privé' ? (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                Destinataire
              </Text>
              <TouchableOpacity
                style={[
                  styles.selectorButton,
                  {
                    borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.8)',
                  }
                ]}
                onPress={() => setShowRecipientModal(true)}
              >
                <Text style={[styles.selectorButtonText, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                  {(() => {
                    if (!selectedRecipient) return 'Sélectionner le destinataire';
                    const r = recipients.find(rcp => rcp.matricule === selectedRecipient);
                    const prenom = r?.prenom ?? '';
                    const nom = r?.nom ?? '';
                    const displayName = [prenom, nom].filter(Boolean).join(' ').trim();
                    // Fallback to matricule if names are missing
                    return displayName ? `${displayName} (${selectedRecipient})` : selectedRecipient;
                  })()}
                </Text>
                <Ionicons name="chevron-down" size={20} color={isDark ? '#94A3B8' : '#64748B'} />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                Classe
              </Text>
              <TouchableOpacity
                style={[
                  styles.selectorButton,
                  {
                    borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.8)',
                  }
                ]}
                onPress={() => setShowClassModal(true)}
              >
                <Text style={[styles.selectorButtonText, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                  {selectedClass || 'Sélectionner une classe'}
                </Text>
                <Ionicons name="chevron-down" size={20} color={isDark ? '#94A3B8' : '#64748B'} />
              </TouchableOpacity>
            </View>
          )}

          {/* Priority Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
              Priorité
            </Text>
            <View style={styles.prioritySelector}>
              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'normal' && styles.priorityButtonActive,
                  { borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)' }
                ]}
                onPress={() => setPriority('normal')}
                accessibilityLabel="Priorité normale"
                accessibilityRole="button"
              >
                <Text style={[
                  styles.priorityButtonText,
                  { color: priority === 'normal' ? 'white' : (isDark ? '#94A3B8' : '#64748B') }
                ]}>
                  Normal
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.priorityButton,
                  priority === 'urgent' && styles.priorityButtonActiveUrgent,
                  { borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)' }
                ]}
                onPress={() => setPriority('urgent')}
                accessibilityLabel="Priorité urgente"
                accessibilityRole="button"
              >
                <Text style={[
                  styles.priorityButtonText,
                  { color: priority === 'urgent' ? 'white' : (isDark ? '#94A3B8' : '#64748B') }
                ]}>
                  Urgent
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Subject Input (only relevant for annonces) */}
          {messageType === 'annonce' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                Sujet (optionnel)
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.8)',
                    color: isDark ? '#F8FAFC' : '#0F172A',
                  }
                ]}
                value={subject}
                onChangeText={setSubject}
                placeholder="Entrez le sujet de l'annonce (optionnel)"
                placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
                maxLength={100}
                returnKeyType="done"
                blurOnSubmit
              />
            </View>
          )}

          {/* Content Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
              Message
            </Text>
            <TextInput
              style={[
                styles.textArea,
                { 
                  borderColor: isDark ? 'rgba(148,163,184,0.2)' : 'rgba(15,23,42,0.1)',
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.8)',
                  color: isDark ? '#F8FAFC' : '#0F172A',
                }
              ]}
              value={content}
              onChangeText={setContent}
              placeholder="Tapez votre message ici..."
              placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
              multiline
              numberOfLines={8}
              textAlignVertical="top"
              maxLength={1000}
            />
          </View>
            </ScrollView>
          </TouchableWithoutFeedback>

          {/* Recipients Modal */}
          <Modal
            visible={showRecipientModal}
            animationType="slide"
            presentationStyle="pageSheet"
          >
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                  Sélectionner le destinataire
                </Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowRecipientModal(false)}
                >
                  <Ionicons name="close" size={24} color={isDark ? '#F8FAFC' : '#0F172A'} />
                </TouchableOpacity>
              </View>
              <FlatList
                data={recipients}
                renderItem={renderRecipientItem}
                keyExtractor={(item) => item.matricule}
                contentContainerStyle={styles.modalList}
                keyboardShouldPersistTaps="handled"
              />
            </SafeAreaView>
          </Modal>

          {/* Classes Modal */}
          <Modal
            visible={showClassModal}
            animationType="slide"
            presentationStyle="pageSheet"
          >
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                  Sélectionner une classe
                </Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowClassModal(false)}
                >
                  <Ionicons name="close" size={24} color={isDark ? '#F8FAFC' : '#0F172A'} />
                </TouchableOpacity>
              </View>
              <FlatList
                data={classes}
                renderItem={renderClassItem}
                keyExtractor={(item) => item.nom_classe}
                contentContainerStyle={styles.modalList}
                keyboardShouldPersistTaps="handled"
              />
            </SafeAreaView>
          </Modal>
        </LinearGradient>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(148,163,184,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
  },
  sendButton: {
    backgroundColor: '#3B82F6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
    gap: 8,
  },
  typeButtonActive: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  selectorButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
  },
  selectorButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  priorityButtonActive: {
    backgroundColor: '#10B981',
    borderColor: '#10B981',
  },
  priorityButtonActiveUrgent: {
    backgroundColor: '#EF4444',
    borderColor: '#EF4444',
  },
  priorityButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  textInput: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
    fontSize: 16,
  },
  textArea: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
    fontSize: 16,
    minHeight: 120,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(148,163,184,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalList: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  recipientItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 8,
  },
  recipientInfo: {
    flex: 1,
  },
  recipientName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  recipientMatricule: {
    fontSize: 14,
    fontWeight: '500',
  },
  classItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 8,
  },
  className: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  classLevel: {
    fontSize: 14,
    fontWeight: '500',
  },
});

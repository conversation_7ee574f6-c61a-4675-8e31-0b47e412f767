import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  useColorScheme,
  FlatList,
  RefreshControl,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { messageService } from '../../../../src/services/messageService';
import type { Message } from '../../../../src/types/api';

export default function AdminMessageThreadScreen() {
  const { participantId } = useLocalSearchParams<{ participantId: string }>();
  const router = useRouter();
  const isDark = useColorScheme() === 'dark';

  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [input, setInput] = useState('');

  const loadMessages = useCallback(async () => {
    if (!participantId) return;
    setError(null);
    setLoading(true);
    try {
      const data = await messageService.getConversationMessages(String(participantId));
      setMessages(data);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement des messages');
    } finally {
      setLoading(false);
    }
  }, [participantId]);

  const onRefresh = useCallback(async () => {
    if (!participantId) return;
    setRefreshing(true);
    try {
      const data = await messageService.getConversationMessages(String(participantId));
      setMessages(data);
    } catch (e) {
      // keep prior state on pull-to-refresh errors
    } finally {
      setRefreshing(false);
    }
  }, [participantId]);

  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  // Mark as read when screen gains focus
  useFocusEffect(
    React.useCallback(() => {
      if (participantId) {
        messageService.markMessagesAsRead(String(participantId));
      }
      return () => {};
    }, [participantId])
  );

  const handleSend = useCallback(async () => {
    const text = input.trim();
    if (!text || !participantId || sending) return;
    setSending(true);
    try {
      await messageService.sendQuickReply(String(participantId), text);
      setInput('');
      // Reload messages after send
      const data = await messageService.getConversationMessages(String(participantId));
      setMessages(data);
      // Also mark as read after sending
      messageService.markMessagesAsRead(String(participantId));
    } catch (e) {
      // Optionally surface an inline error, keep minimal for now
    } finally {
      setSending(false);
    }
  }, [input, participantId, sending]);

  const renderItem = ({ item }: { item: Message | any }) => {
    // Infer sender side: compare sender_matricule with current user if backend provides 'Vous' in sender_name
    const isMine = item.sender_name === 'Vous' || (item.sender_role === 'admin' && item.sender_name === 'Vous');
    return (
      <View style={[styles.row, isMine ? styles.rowRight : styles.rowLeft]}>
        <View
          style={[
            styles.bubble,
            {
              backgroundColor: isMine ? (isDark ? '#2563EB' : '#3B82F6') : (isDark ? 'rgba(255,255,255,0.06)' : '#FFFFFF'),
              borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.08)',
            },
            isMine ? styles.bubbleRight : styles.bubbleLeft,
          ]}
        >
          {!!item.subject && (
            <Text style={[styles.subject, { color: isMine ? 'rgba(255,255,255,0.9)' : (isDark ? '#E2E8F0' : '#0F172A') }]} numberOfLines={1}>
              {item.subject}
            </Text>
          )}
          <Text style={[styles.content, { color: isMine ? 'white' : (isDark ? '#E2E8F0' : '#0F172A') }]}>
            {item.contenu || ''}
          </Text>
          {!!item.date_envoi && (
            <Text style={[styles.time, { color: isMine ? 'rgba(255,255,255,0.8)' : (isDark ? '#94A3B8' : '#64748B') }]}>
              {formatTime(item.date_envoi)}
            </Text>
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>
          Chargement de la conversation...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text>
        <TouchableOpacity onPress={loadMessages} style={styles.retryBtn}>
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.select({ ios: 80, android: 24 }) as number}
      >
        {/* Header */}
        <View style={[styles.header, { backgroundColor: isDark ? '#0F172A' : '#F8FAFC' }]}>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
            >
              <Ionicons name="arrow-back" size={22} color={isDark ? '#E2E8F0' : '#0F172A'} />
            </TouchableOpacity>

            <Text style={[styles.headerTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]} numberOfLines={1}>
              {decodeURIComponent(String(participantId || 'Conversation'))}
            </Text>

            <View style={{ width: 40 }} />
          </View>
        </View>

        {/* Messages */}
        {messages.length === 0 ? (
          <View style={[styles.threadContent, { alignItems: 'center', gap: 12, flex: 1 }]}>
            <Ionicons name="chatbubbles-outline" size={48} color={isDark ? '#475569' : '#94A3B8'} />
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucun message dans ce fil</Text>
          </View>
        ) : (
          <FlatList
            data={[...messages].reverse()}
            keyExtractor={(m, idx) => `${m.date_envoi}-${idx}`}
            contentContainerStyle={[styles.threadContent, { paddingBottom: 88 }]}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={isDark ? '#E5E7EB' : '#334155'} />
            }
            renderItem={renderItem}
            keyboardDismissMode="on-drag"
            keyboardShouldPersistTaps="handled"
          />
        )}

        {/* Composer */}
        <View
          style={[
            styles.composer,
            {
              backgroundColor: isDark ? 'rgba(2,6,23,0.9)' : '#FFFFFF',
              borderTopColor: isDark ? 'rgba(148,163,184,0.15)' : 'rgba(15,23,42,0.08)',
              paddingBottom: Platform.OS === 'ios' ? 16 : 12,
            },
          ]}
        >
          <TextInput
            style={[styles.input, { color: isDark ? '#F8FAFC' : '#0F172A' }]}
            placeholder="Écrire un message..."
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            value={input}
            onChangeText={setInput}
            multiline
            textAlignVertical="top"
            returnKeyType="send"
            blurOnSubmit={false}
            onSubmitEditing={handleSend}
          />
          <TouchableOpacity
            style={[styles.sendBtn, { opacity: input.trim() && !sending ? 1 : 0.6 }]}
            disabled={!input.trim() || sending}
            onPress={handleSend}
            accessibilityRole="button"
            accessibilityLabel="Envoyer"
          >
            {sending ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="send" size={18} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

function formatTime(dateStr?: string) {
  if (!dateStr) return '';
  try {
    const d = new Date(dateStr);
    const hh = `${d.getHours()}`.padStart(2, '0');
    const mm = `${d.getMinutes()}`.padStart(2, '0');
    const day = `${d.getDate()}`.padStart(2, '0');
    const mon = `${d.getMonth() + 1}`.padStart(2, '0');
    const yr = d.getFullYear();
    return `${day}/${mon}/${yr} ${hh}:${mm}`;
  } catch {
    return dateStr;
  }
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  retryBtn: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(148,163,184,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
  },
  threadContent: {
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 12,
  },
  row: {
    width: '100%',
    marginVertical: 4,
    flexDirection: 'row',
  },
  rowLeft: {
    justifyContent: 'flex-start',
  },
  rowRight: {
    justifyContent: 'flex-end',
  },
  bubble: {
    maxWidth: '82%',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 14,
    borderWidth: 1,
    gap: 6,
  },
  bubbleLeft: {
    borderTopLeftRadius: 6,
  },
  bubbleRight: {
    borderTopRightRadius: 6,
  },
  subject: {
    fontSize: 12,
    fontWeight: '700',
  },
  content: {
    fontSize: 15,
    lineHeight: 20,
  },
  time: {
    alignSelf: 'flex-end',
    fontSize: 11,
    fontWeight: '500',
  },
  composer: {
    borderTopWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
  },
  input: {
    flex: 1,
    maxHeight: 140,
    minHeight: 44,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.25)',
    borderRadius: 10,
    backgroundColor: 'rgba(255,255,255,0.06)',
    fontSize: 15,
  },
  sendBtn: {
    backgroundColor: '#3B82F6',
    height: 40,
    minWidth: 44,
    paddingHorizontal: 14,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

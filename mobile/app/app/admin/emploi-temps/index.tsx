import React, { useMemo, useState, useCallback, useEffect, memo } from 'react';
import { View, FlatList, RefreshControl, Alert, TouchableOpacity, Text, TextInput, ActivityIndicator, useColorScheme, StyleSheet, Modal, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useAdminTimetable } from '../../../../src/hooks/useAdminTimetable';
import { Feather } from '@expo/vector-icons';
import FAB from '@/src/components/FAB';

// Card component for each timetable item
const TimetableCard = memo(({ item, onEdit, onDelete, isDark }: {
  item: { id: string | number; subject: string; className: string; day: string; start: string; end: string; room?: string | null };
  onEdit: (id: string | number) => void;
  onDelete: (id: string | number) => void;
  isDark: boolean;
}) => {
  return (
    <View style={[styles.card, { backgroundColor: isDark ? '#1E293B' : '#FFFFFF', borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}>
      <View style={{ flex: 1 }}>
        <Text style={[styles.itemTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{item.subject}</Text>
        <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
          {item.className} • {item.day} • {item.start} - {item.end}{item.room ? ` • Salle ${item.room}` : ''}
        </Text>
      </View>
      <View style={styles.actionsContainer}>
        <TouchableOpacity onPress={() => onEdit(item.id)} style={[styles.actionButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}>
          <Feather name="edit" size={16} color={isDark ? '#94A3B8' : '#64748B'} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onDelete(item.id)} style={[styles.actionButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}>
          <Feather name="trash-2" size={16} color="#ef4444" />
        </TouchableOpacity>
      </View>
    </View>
  );
});

// Simplified Header with Search and Filter button
const SearchHeader = memo(({ search, onChangeSearch, onClear, onOpenFilters, isDark }: {
  search: string;
  onChangeSearch: (t: string) => void;
  onClear: () => void;
  onOpenFilters: () => void;
  isDark: boolean;
}) => {
  return (
    <View style={{ paddingHorizontal: 16, paddingBottom: 12, paddingTop: 8, backgroundColor: isDark ? '#0B1220' : '#FFFFFF' }}>
      <View style={[styles.searchWrap, { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 8, borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
        <Feather name="search" size={18} color={isDark ? '#64748B' : '#94A3B8'} style={{ marginLeft: 4 }} />
        <TextInput
          placeholder="Rechercher..."
          placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
          value={search}
          onChangeText={onChangeSearch}
          style={{ paddingVertical: 10, paddingHorizontal: 6, color: isDark ? '#F8FAFC' : '#0F172A', flex: 1 }}
          blurOnSubmit={false}
          returnKeyType="search"
        />
        {search.length > 0 && (
          <Pressable onPress={onClear} hitSlop={8} style={{ padding: 6 }} accessibilityLabel="Effacer la recherche">
            <Feather name="x" size={18} color={isDark ? '#94A3B8' : '#64748B'} />
          </Pressable>
        )}
        <TouchableOpacity onPress={onOpenFilters} style={{ padding: 6, marginLeft: 4 }} accessibilityLabel="Ouvrir les filtres">
          <Feather name="filter" size={18} color={isDark ? '#94A3B8' : '#64748B'} />
        </TouchableOpacity>
      </View>
    </View>
  );
});

// Modal for filters
const FilterModal = memo(({ visible, onClose, classes, selectedClass, setSelectedClass, selectedDay, setSelectedDay, loadingClasses, classesError, isDark }: {
  visible: boolean;
  onClose: () => void;
  classes: { nom_classe: string }[];
  selectedClass?: string;
  setSelectedClass: (v?: string) => void;
  selectedDay?: string;
  setSelectedDay: (v?: string) => void;
  loadingClasses: boolean;
  classesError?: string | null;
  isDark: boolean;
}) => {
  const days = ['LUNDI', 'MARDI', 'MERCREDI', 'JEUDI', 'VENDREDI', 'SAMEDI'];
  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <Pressable style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.4)' }} onPress={onClose} />
      <View style={[styles.sheet, { backgroundColor: isDark ? '#0B1220' : '#FFFFFF', borderTopColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}>
        <View style={{ padding: 16 }}>
          <Text style={{ fontWeight: '800', fontSize: 16, color: isDark ? '#F8FAFC' : '#0F172A', marginBottom: 16 }}>Filtrer</Text>
          
          <Text style={{ fontSize: 12, color: isDark ? '#64748B' : '#94A3B8', marginBottom: 6 }}>Classe</Text>
          {loadingClasses ? <ActivityIndicator /> : (
            <FlatList
              data={classes}
              keyExtractor={(c) => c.nom_classe}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ gap: 8, paddingBottom: 16 }}
              renderItem={({ item }) => {
                const active = selectedClass === item.nom_classe;
                return (
                  <TouchableOpacity
                    onPress={() => setSelectedClass(active ? undefined : item.nom_classe)}
                    style={[styles.filterChip, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: active ? (isDark ? 'rgba(37,99,235,0.25)' : 'rgba(37,99,235,0.12)') : 'transparent' }]}
                  >
                    <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A' }}>{item.nom_classe}</Text>
                  </TouchableOpacity>
                );
              }}
            />
          )}
          {!!classesError && !loadingClasses && <Text style={{ color: '#ef4444', marginLeft: 8 }}>{classesError}</Text>}

          <Text style={{ fontSize: 12, color: isDark ? '#64748B' : '#94A3B8', marginBottom: 6, marginTop: 8 }}>Jour</Text>
          <FlatList
            data={days}
            keyExtractor={(d) => d}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ gap: 8, paddingBottom: 16 }}
            renderItem={({ item: d }) => {
              const active = selectedDay === d;
              return (
                <TouchableOpacity
                  onPress={() => setSelectedDay(active ? undefined : d)}
                  style={[styles.filterChip, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: active ? (isDark ? 'rgba(37,99,235,0.25)' : 'rgba(37,99,235,0.12)') : 'transparent' }]}
                >
                  <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A' }}>{d}</Text>
                </TouchableOpacity>
              );
            }}
          />
          <TouchableOpacity onPress={onClose} style={[styles.actionBtn, { marginTop: 16, backgroundColor: '#2563EB' }]}>
            <Text style={{ fontWeight: '700', color: '#FFFFFF', textAlign: 'center' }}>Appliquer</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
});

export default function AdminEmploiTempsIndexScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const {
    classes, planning, selectedClass, setSelectedClass, selectedDay, setSelectedDay,
    loadingClasses, loadingPlanning, classesError, planningError,
    loadClasses, refreshPlanning, deletePlanningEntry,
  } = useAdminTimetable();

  useEffect(() => {
    loadClasses();
  }, [loadClasses]);

  const [search, setSearch] = useState('');
  const [query, setQuery] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);

  const handleChange = useCallback((t: string) => setSearch(t), []);
  const handleClear = useCallback(() => setSearch(''), []);
  const handleCreate = useCallback(() => {
    router.push({ pathname: '/app/admin/emploi-temps/create' } as any);
  }, [router]);

  useEffect(() => {
    const t = setTimeout(() => setQuery(search), 250);
    return () => clearTimeout(t);
  }, [search]);

  const filtered = useMemo(() => {
    if (!Array.isArray(planning)) return [];
    const q = query.trim().toLowerCase();
    return planning.filter((p) => {
      const byQuery = !q || (p.subject || '').toLowerCase().includes(q) || (p.className || '').toLowerCase().includes(q) || (p.day || '').toLowerCase().includes(q);
      const byClass = !selectedClass || p.className === selectedClass;
      const byDay = !selectedDay || p.day === selectedDay;
      return byQuery && byClass && byDay;
    });
  }, [planning, query, selectedClass, selectedDay]);

  const onEdit = useCallback((id: string | number) => {
    router.push({ pathname: '/app/admin/emploi-temps/[id]/edit', params: { id: String(id) } } as any);
  }, [router]);

  const onDelete = useCallback(async (id: string | number) => {
    Alert.alert(
      'Confirmer la suppression',
      'Êtes-vous sûr de vouloir supprimer ce créneau ? Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await deletePlanningEntry(id);
            } catch (error) {
              Alert.alert('Erreur', 'La suppression a échoué. Veuillez réessayer.');
            }
          },
        },
      ]
    );
  }, [deletePlanningEntry]);

  const Header = useMemo(() => (
    <SearchHeader
      search={search}
      onChangeSearch={handleChange}
      onClear={handleClear}
      onOpenFilters={() => setFilterModalVisible(true)}
      isDark={isDark}
    />
  ), [search, handleChange, handleClear, isDark]);

  const ErrorBanner = planningError ? (
    <View style={styles.errorBanner}>
      <Text style={{ color: '#ef4444', flex: 1 }}>{String(planningError)}</Text>
      <TouchableOpacity onPress={() => refreshPlanning()} style={[styles.retryBtn, { backgroundColor: '#ef4444' }]}>
        <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
      </TouchableOpacity>
    </View>
  ) : null;

  const emptyComponent = (
    <View style={styles.center}>
      <Text style={{ fontSize: 48, marginBottom: 8 }}>🗓️</Text>
      <Text style={{ color: isDark ? '#94A3B8' : '#64748B', textAlign: 'center' }}>
        {loadingPlanning ? 'Chargement...' : 'Aucun créneau trouvé. Essayez de modifier vos filtres ou d\'en ajouter un nouveau.'}
      </Text>
    </View>
  );

  if (loadingPlanning && (!planning || planning.length === 0)) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: isDark ? '#0B1220' : '#F1F5F9' }}>
      {ErrorBanner}
      <FlatList
        data={filtered}
        keyExtractor={(item) => String(item.id)}
        renderItem={({ item }) => (
          <TimetableCard item={item} onEdit={onEdit} onDelete={onDelete} isDark={isDark} />
        )}
        ListHeaderComponent={Header}
        stickyHeaderIndices={[0]}
        refreshControl={<RefreshControl refreshing={loadingPlanning} onRefresh={refreshPlanning} />}
        contentContainerStyle={filtered.length === 0 ? { flexGrow: 1, justifyContent: 'center' } : { paddingHorizontal: 16, paddingTop: 8, paddingBottom: 80 }}
        ListEmptyComponent={emptyComponent}
        keyboardShouldPersistTaps="handled"
      />
      <FAB icon="plus" onPress={handleCreate} />
      <FilterModal
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        classes={classes}
        selectedClass={selectedClass}
        setSelectedClass={setSelectedClass}
        selectedDay={selectedDay}
        setSelectedDay={setSelectedDay}
        loadingClasses={loadingClasses}
        classesError={classesError}
        isDark={isDark}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  retryBtn: { paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  searchWrap: { borderWidth: 1, borderRadius: 12 },
  sheet: { borderTopWidth: 1, borderTopLeftRadius: 16, borderTopRightRadius: 16, position: 'absolute', left: 0, right: 0, bottom: 0 },
  actionBtn: { paddingVertical: 12, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, marginBottom: 10 },
  card: { padding: 14, borderRadius: 16, borderWidth: 1, marginBottom: 10, flexDirection: 'row', alignItems: 'center', gap: 10 },
  itemTitle: { fontSize: 16, fontWeight: '800' },
  itemMeta: { fontSize: 13, marginTop: 4 },
  actionsContainer: { flexDirection: 'row', gap: 8 },
  actionButton: { padding: 8, borderRadius: 99 },
  errorBanner: { paddingHorizontal: 16, paddingVertical: 10, backgroundColor: 'rgba(239,68,68,0.08)', borderColor: 'rgba(239,68,68,0.35)', borderWidth: 1, margin: 16, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 },
  filterChip: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
});
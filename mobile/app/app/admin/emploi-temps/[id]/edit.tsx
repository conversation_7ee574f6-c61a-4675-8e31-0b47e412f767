import { useCallback, useEffect, useMemo, useState } from 'react';
import { View, ScrollView, ActivityIndicator, Alert, TextInput, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Colors } from '../../../../../constants/Colors';
import Select from '../../../../../src/components/Select';
import {
  ScheduleAdminService,
  type Classe,
  type Subject,
  type Slot,
  type CreateUpdateCreneauDTO,
  type VerifyConflictDTO,
} from '../../../../../src/services/scheduleAdminService';

export default function AdminEmploiTempsEditScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ id: string }>();
  const recordId = params.id ? decodeURIComponent(params.id) : undefined;

  const [classes, setClasses] = useState<Classe[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [slots, setSlots] = useState<Slot[]>([]);

  const [nomClasse, setNomClasse] = useState<string | undefined>(undefined);
  const [matiereId, setMatiereId] = useState<string | number | undefined>(undefined);
  const [jour, setJour] = useState<string | undefined>(undefined);
  const [debut, setDebut] = useState<string | undefined>(undefined);
  const [fin, setFin] = useState<string | undefined>(undefined);
  const [salle, setSalle] = useState<string | undefined>(undefined);

  const [loadingBootstrap, setLoadingBootstrap] = useState(false);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [conflicts, setConflicts] = useState<{
    aConflits: boolean;
    conflitsClasse: any[];
    conflitsProfesseur: any[];
  } | null>(null);
  const [error, setError] = useState<string | undefined>(undefined);

  const loadBootstrap = useCallback(async () => {
    setLoadingBootstrap(true);
    setError(undefined);
    try {
      const [cls, sl] = await Promise.all([
        ScheduleAdminService.listClasses(),
        ScheduleAdminService.getSlots(),
      ]);
      setClasses(cls);
      setSlots(sl);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement initial');
    } finally {
      setLoadingBootstrap(false);
    }
  }, []);

  const loadSubjectsForClass = useCallback(async (classe?: string) => {
    if (!classe) {
      setSubjects([]);
      return;
    }
    setLoadingSubjects(true);
    try {
      const subs = await ScheduleAdminService.listSubjects(classe);
      setSubjects(subs);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement des matières');
    } finally {
      setLoadingSubjects(false);
    }
  }, []);

  useEffect(() => {
    loadBootstrap();
  }, [loadBootstrap]);

  useEffect(() => {
    setMatiereId(undefined);
    loadSubjectsForClass(nomClasse);
  }, [nomClasse, loadSubjectsForClass]);

  // Options for Select (string values as required by shared Select)
  const classItems = classes.map((c) => ({ label: c.nom_classe, value: c.nom_classe }));
  const subjectItems = subjects.map((s) => ({ label: s.nom, value: String(s.id) }));
  const dayItems = ['LUNDI', 'MARDI', 'MERCREDI', 'JEUDI', 'VENDREDI', 'SAMEDI'].map((d) => ({ label: d, value: d }));
  const startItems = slots.map((s) => ({ label: s.label ?? `${s.start}`, value: s.start }));
  const endItems = slots.map((s) => ({ label: s.label ?? `${s.end}`, value: s.end }));

  const validTimeOrder = useMemo(() => {
    if (!debut || !fin) return true;
    return debut < fin;
  }, [debut, fin]);

  const matiereIdString = useMemo(
    () => (matiereId != null ? String(matiereId) : undefined),
    [matiereId]
  );

  const canSubmit = useMemo(() => {
    return (
      !!recordId &&
      !!nomClasse &&
      !!matiereId &&
      !!jour &&
      !!debut &&
      !!fin &&
      validTimeOrder &&
      !submitting
    );
  }, [recordId, nomClasse, matiereId, jour, debut, fin, validTimeOrder, submitting]);

  const handleVerify = useCallback(async () => {
    if (!canSubmit) {
      Alert.alert('Validation', 'Veuillez remplir tous les champs obligatoires.');
      return;
    }
    setSubmitting(true);
    setConflicts(null);
    try {
      const dto: VerifyConflictDTO = {
        nom_classe: nomClasse!,
        matiere_id: matiereId!,
        jour_semaine: jour!,
        heure_debut: debut!,
        heure_fin: fin!,
        salle: salle,
        exclude_id: recordId!,
      };
      const res = await ScheduleAdminService.verifyConflicts(dto);
      setConflicts({
        aConflits: !!res.aConflits,
        conflitsClasse: res.conflitsClasse || [],
        conflitsProfesseur: res.conflitsProfesseur || [],
      });
      if (!res.aConflits) {
        Alert.alert('Vérification', 'Aucun conflit détecté.');
      }
    } catch (e: any) {
      Alert.alert('Erreur', e?.message || 'Échec de la vérification des conflits');
    } finally {
      setSubmitting(false);
    }
  }, [canSubmit, nomClasse, matiereId, jour, debut, fin, salle, recordId]);

  const handleSubmit = useCallback(async () => {
    if (!canSubmit) {
      Alert.alert('Validation', 'Veuillez remplir tous les champs obligatoires.');
      return;
    }
    setSubmitting(true);
    try {
      const verify = await ScheduleAdminService.verifyConflicts({
        nom_classe: nomClasse!,
        matiere_id: matiereId!,
        jour_semaine: jour!,
        heure_debut: debut!,
        heure_fin: fin!,
        salle: salle,
        exclude_id: recordId!,
      });
      if (verify.aConflits) {
        setConflicts({
          aConflits: true,
          conflitsClasse: verify.conflitsClasse || [],
          conflitsProfesseur: verify.conflitsProfesseur || [],
        });
        Alert.alert('Conflits détectés', 'Veuillez résoudre les conflits avant d’enregistrer.');
        return;
      }

      const payload: CreateUpdateCreneauDTO = {
        nom_classe: nomClasse!,
        matiere_id: matiereId!,
        jour_semaine: jour!,
        heure_debut: debut!,
        heure_fin: fin!,
        salle: salle,
      };
      await ScheduleAdminService.updateCreneau(recordId!, payload);
      Alert.alert('Succès', 'Créneau mis à jour.', [{ text: 'OK', onPress: () => router.back() }]);
    } catch (e: any) {
      Alert.alert('Erreur', e?.message || 'Échec de la mise à jour du créneau');
    } finally {
      setSubmitting(false);
    }
  }, [canSubmit, nomClasse, matiereId, jour, debut, fin, salle, recordId, router]);

  const handleDelete = useCallback(async () => {
    if (!recordId) return;
    Alert.alert('Confirmation', 'Supprimer ce créneau ?', [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Supprimer',
        style: 'destructive',
        onPress: async () => {
          setDeleting(true);
          try {
            await ScheduleAdminService.deleteCreneau(recordId);
            Alert.alert('Supprimé', 'Le créneau a été supprimé.', [
              { text: 'OK', onPress: () => router.back() },
            ]);
          } catch (e: any) {
            Alert.alert('Erreur', e?.message || 'Échec de la suppression');
          } finally {
            setDeleting(false);
          }
        },
      },
    ]);
  }, [recordId, router]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16, gap: 12 }}>
        <Text style={{ fontSize: 18, fontWeight: '600' }}>Modifier un créneau</Text>

      {loadingBootstrap && <ActivityIndicator color={Colors.light?.tint || '#007AFF'} />}
      {!!error && <Text style={{ color: 'red' }}>{error}</Text>}

      {/* Classe */}
      <View>
        <Text style={{ marginBottom: 6 }}>Classe</Text>
        <Select options={classItems} value={nomClasse} onValueChange={(v) => setNomClasse(v)} />
      </View>

      {/* Matière */}
      <View>
        <Text style={{ marginBottom: 6 }}>Matière</Text>
        {loadingSubjects ? (
          <ActivityIndicator color={Colors.light?.tint || '#007AFF'} />
        ) : (
          <Select
            options={subjectItems}
            value={matiereIdString}
            onValueChange={(v) => setMatiereId(v)}
          />
        )}
      </View>

      {/* Jour */}
      <View>
        <Text style={{ marginBottom: 6 }}>Jour</Text>
        <Select options={dayItems} value={jour} onValueChange={(v) => setJour(v)} />
      </View>

      {/* Heures */}
      <View style={{ gap: 8 }}>
        <Text>Heure début</Text>
        <Select options={startItems} value={debut} onValueChange={(v) => setDebut(v)} />
        <Text>Heure fin</Text>
        <Select options={endItems} value={fin} onValueChange={(v) => setFin(v)} />
        {!validTimeOrder && (
          <Text style={{ color: 'red' }}>L'heure de début doit être avant l'heure de fin.</Text>
        )}
      </View>

      {/* Salle */}
      <View>
        <Text>Salle (optionnel)</Text>
        <TextInput
          value={salle}
          placeholder="Ex: Salle A"
          onChangeText={setSalle}
          autoCapitalize="none"
          style={{ borderWidth: 1, borderColor: '#ccc', padding: 10, borderRadius: 8 }}
        />
      </View>

      {/* Conflits */}
      {conflicts && conflicts.aConflits && (
        <View style={{ padding: 12, backgroundColor: '#FFF3CD', borderRadius: 8 }}>
          <Text style={{ color: '#664D03', marginBottom: 6 }}>Des conflits ont été détectés.</Text>
          {!!conflicts.conflitsClasse?.length && (
            <View style={{ marginBottom: 6 }}>
              <Text style={{ fontWeight: '600' }}>Conflits de classe</Text>
              {conflicts.conflitsClasse.map((c, idx) => (
                <Text key={idx}>{JSON.stringify(c)}</Text>
              ))}
            </View>
          )}
          {!!conflicts.conflitsProfesseur?.length && (
            <View>
              <Text style={{ fontWeight: '600' }}>Conflits de professeur</Text>
              {conflicts.conflitsProfesseur.map((c, idx) => (
                <Text key={idx}>{JSON.stringify(c)}</Text>
              ))}
            </View>
          )}
        </View>
      )}

      {/* Actions */}
      <View style={{ flexDirection: 'row', gap: 12, marginTop: 12, flexWrap: 'wrap' }}>
        <View
          style={{
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            backgroundColor: (!canSubmit || submitting) ? '#c9c9c9' : (Colors.light?.tint || '#007AFF'),
          }}
        >
          <Text
            onPress={!canSubmit || submitting ? undefined : handleVerify}
            style={{ color: '#fff' }}
          >
            Vérifier conflits
          </Text>
        </View>

        <View
          style={{
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            backgroundColor: (!canSubmit || submitting) ? '#c9c9c9' : (Colors.light?.tint || '#34C759'),
          }}
        >
          {submitting ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text onPress={!canSubmit || submitting ? undefined : handleSubmit} style={{ color: '#fff' }}>
              Enregistrer
            </Text>
          )}
        </View>

        <View
          style={{
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            backgroundColor: (!recordId || deleting) ? '#c9c9c9' : '#FF3B30',
          }}
        >
          {deleting ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text onPress={!recordId || deleting ? undefined : handleDelete} style={{ color: '#fff' }}>
              Supprimer
            </Text>
          )}
        </View>
      </View>
    </ScrollView>
  </SafeAreaView>
);
}
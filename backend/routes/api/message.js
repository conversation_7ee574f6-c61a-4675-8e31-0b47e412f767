const express = require('express');
const multer = require('multer');
const router = express.Router();
const { hybridAuth, apiAuth } = require('../../middlewares/hybridAuth');

// Configure multer for handling multipart form data
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files
  }
});
const { query } = require('../../models/db');

// Afficher la page de messagerie admin
router.get('/', async (req, res) => {
  try {
    const matricule = req.session.matricule; // Vérifie que l'admin est connecté

    if (!matricule) {
      return res.redirect('/ad');
    }

    // Récupérer les professeurs pour pouvoir leur envoyer un message privé
    const profQuery = `SELECT matricule FROM utilisateur WHERE role = 'professeur'`;

    // Récupérer les messages reçus par cet admin (privés ou annonces générales)
    const messagesQuery = `
      SELECT * FROM message
      WHERE receiver_matricule = ?
         OR (nom_classe IS NULL AND type = 'annonce')
      ORDER BY date_envoi DESC
    `;

    const profs = await query(profQuery);
    const messages = await query(messagesQuery, [matricule]);

    res.render('admin/message', { profs, messages });
  } catch (error) {
    console.error('❌ Erreur lors du chargement de la messagerie admin:', error.message);
    return res.status(500).send('Erreur lors du chargement de la messagerie.');
  }
});

// General conversations endpoint (used by frontend)
router.get('/conversations', hybridAuth(), async (req, res) => {
  const { matricule, role } = req.user;

  // Route to appropriate handler based on role
  if (role === 'admin') {
    return getAdminConversations(req, res);
  } else if (role === 'professeur') {
    return getProfesseurConversations(req, res);
  } else if (role === 'eleve') {
    return getEleveConversations(req, res);
  } else {
    return res.status(403).json({ success: false, message: 'Rôle non autorisé' });
  }
});

/**
 * API endpoint for admin conversations
 * Ensure user is authenticated; when using hybridAuth elsewhere, req.user is set.
 * Here we defensively use hybridAuth() as well to avoid req.user being undefined.
 */
router.get('/admin/conversations', hybridAuth(), getAdminConversations);

async function getAdminConversations(req, res) {
  const user = req.user || {};
  console.log('🔍 user:', user);
  const { matricule, role } = user;

  if (!matricule) {
    return res.status(401).json({ success: false, message: 'Accès non autorisé' });
  }

  try {
    // Get conversations with message counts and stats - handles both private messages and announcements
    const conversationsQuery = `
      SELECT DISTINCT
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.nom_classe
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as participant_id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.nom_classe
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN
            CONCAT('Classe ', m.nom_classe)
          WHEN m.sender_matricule = ? THEN
            COALESCE(CONCAT(pr.prenom, ' ', pr.nom), ar.nom, m.receiver_matricule, 'Utilisateur')
          ELSE
            COALESCE(CONCAT(ps.prenom, ' ', ps.nom), ads.nom, m.sender_matricule, 'Utilisateur')
        END as participant_name,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN 'classe'
          WHEN m.sender_matricule = ? THEN ur.role
          ELSE us.role
        END as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        SUBSTRING(m.contenu, 1, 100) as last_message,
        COUNT(CASE
          WHEN m.lu = FALSE AND m.sender_matricule != ? THEN 1
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.lu = FALSE THEN 1
        END) as unread_count
      FROM message m
      LEFT JOIN utilisateur us ON m.sender_matricule = us.matricule
      LEFT JOIN utilisateur ur ON m.receiver_matricule = ur.matricule
      LEFT JOIN professeur ps ON m.sender_matricule = ps.matricule
      LEFT JOIN professeur pr ON m.receiver_matricule = pr.matricule
      LEFT JOIN admin ads ON m.sender_matricule = ads.matricule
      LEFT JOIN admin ar ON m.receiver_matricule = ar.matricule
      WHERE (m.sender_matricule = ? OR m.receiver_matricule = ?)
         OR (m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.sender_matricule = ?)
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;

    const conversations = await query(conversationsQuery, [
      matricule, matricule, matricule, matricule, matricule, // For participant_id, id, participant_name, participant_role, unread_count logic
      matricule, matricule, matricule // For WHERE clause
    ]);

    // Get message statistics
    const statsQuery = `
      SELECT
        COUNT(CASE WHEN lu = FALSE AND receiver_matricule = ? THEN 1 END) as unread,
        COUNT(CASE WHEN type = 'privé' AND (sender_matricule = ? OR receiver_matricule = ?) THEN 1 END) as private,
        COUNT(CASE WHEN type = 'annonce' AND sender_matricule = ? THEN 1 END) as announcements,
        (SELECT COUNT(*) FROM utilisateur WHERE role = 'professeur') as professors
      FROM message
    `;

    const statsResult = await query(statsQuery, [matricule, matricule, matricule, matricule]);
    const stats = statsResult[0] || {};

    res.json({
      success: true,
      conversations: conversations,
      stats: stats
    });

  } catch (error) {
    console.error('❌ Error loading admin conversations:', error);
    res.status(500).json({ success: false, message: 'Erreur lors du chargement des conversations' });
  }
}

async function getProfesseurConversations(req, res) {
  const { matricule } = req.user;

  try {
    console.log('🔍 Getting professor conversations for:', matricule);

    // First, let's check what messages exist for this professor
    const allMessagesQuery = `
      SELECT * FROM message
      WHERE sender_matricule = ? OR receiver_matricule = ?
      ORDER BY date_envoi DESC
    `;
    const allMessages = await query(allMessagesQuery, [matricule, matricule]);
    console.log('📧 All messages for professor:', allMessages.length, allMessages);

    // Enhanced query for professors - handles both private messages and announcements
    const conversationsQuery = `
      SELECT DISTINCT
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.sender_matricule
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as participant_id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.sender_matricule
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN
            COALESCE(CONCAT(a3.prenom, ' ', a3.nom), a3.matricule, 'Administration')
          WHEN m.sender_matricule = ? THEN
            COALESCE(CONCAT(a.prenom, ' ', a.nom), u.matricule, 'Administration')
          ELSE
            COALESCE(CONCAT(a2.prenom, ' ', a2.nom), u2.matricule, 'Administration')
        END as participant_name,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN 'admin'
          WHEN m.sender_matricule = ? THEN
            COALESCE(u.role, 'admin')
          ELSE
            COALESCE(u2.role, 'admin')
        END as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        (SELECT contenu FROM message m2
         WHERE (m2.sender_matricule = ? AND m2.receiver_matricule = participant_id)
            OR (m2.receiver_matricule = ? AND m2.sender_matricule = participant_id)
            OR (m2.type = 'annonce' AND m2.sender_matricule = participant_id AND m2.nom_classe IN (
              SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
            ))
         ORDER BY m2.date_envoi DESC LIMIT 1) as last_message,
        COUNT(CASE
          WHEN m.receiver_matricule = ? AND m.lu = FALSE THEN 1
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.lu = FALSE AND m.nom_classe IN (
            SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
          ) THEN 1
        END) as unread_count
      FROM message m
      LEFT JOIN admin a ON m.receiver_matricule = a.matricule
      LEFT JOIN admin a2 ON m.sender_matricule = a2.matricule
      LEFT JOIN admin a3 ON m.sender_matricule = a3.matricule AND m.type = 'annonce'
      LEFT JOIN utilisateur u ON m.receiver_matricule = u.matricule
      LEFT JOIN utilisateur u2 ON m.sender_matricule = u2.matricule
      WHERE (m.sender_matricule = ? OR m.receiver_matricule = ?)
         OR (m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.nom_classe IN (
           SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
         ))
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;

    const conversations = await query(conversationsQuery, [
      matricule, matricule, matricule, matricule, // For participant_id and id logic
      matricule, matricule, matricule, // For last_message subquery
      matricule, matricule, // For unread_count logic
      matricule, matricule, matricule // For WHERE clause
    ]);

    console.log('💬 Professor conversations found:', conversations.length, conversations);

    const stats = {
      unread: conversations.reduce((sum, conv) => sum + (conv.unread_count || 0), 0),
      private: allMessages.filter(msg => msg.type === 'privé').length,
      announcements: allMessages.filter(msg => msg.type === 'annonce').length,
      total: allMessages.length
    };

    console.log('📊 Professor stats:', stats);

    res.json({
      success: true,
      conversations: conversations,
      stats: stats
    });

  } catch (error) {
    console.error('❌ Error loading professor conversations:', error);
    res.status(500).json({ success: false, message: 'Erreur lors du chargement des conversations' });
  }
}

async function getEleveConversations(req, res) {
  const { matricule } = req.user;

  try {
    // Students can only receive announcements and messages from admin
    const conversationsQuery = `
      SELECT
        m.sender_matricule as participant_id,
        COALESCE(a.nom, 'Administration') as participant_name,
        'admin' as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        SUBSTRING(m.contenu, 1, 100) as last_message,
        COUNT(CASE WHEN m.lu = FALSE THEN 1 END) as unread_count
      FROM message m
      LEFT JOIN admin a ON m.sender_matricule = a.matricule
      WHERE m.receiver_matricule = ? OR (m.type = 'annonce' AND m.nom_classe IN (
        SELECT nom_classe FROM eleve WHERE matricule = ?
      ))
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;

    const conversations = await query(conversationsQuery, [matricule, matricule]);

    const stats = {
      unread: conversations.reduce((sum, conv) => sum + conv.unread_count, 0),
      private: conversations.filter(conv => conv.type === 'privé').length,
      announcements: conversations.filter(conv => conv.type === 'annonce').length,
      classes: 1 // Student belongs to one class
    };

    res.json({
      success: true,
      conversations: conversations,
      stats: stats
    });

  } catch (error) {
    console.error('❌ Error loading student conversations:', error);
    res.status(500).json({ success: false, message: 'Erreur lors du chargement des conversations' });
  }
}

// Get conversation messages
router.get('/conversation/:conversationId/messages', hybridAuth(), async (req, res) => {
  const { matricule } = req.user;
  const participantId = req.params.conversationId; // This is actually the participant's matricule

  console.log('🔍 Loading messages for conversation between:', matricule, 'and', participantId);

  if (!matricule) {
    return res.status(401).json({ success: false, message: 'Accès non autorisé' });
  }

  try {
    // Enhanced query to get messages - handles both private messages and announcements
    const messagesQuery = `
      SELECT m.*,
             CASE
               WHEN m.sender_matricule = ? THEN 'Vous'
               WHEN m.sender_matricule != ? THEN
                 COALESCE(
                   CONCAT(p.prenom, ' ', p.nom),
                   CONCAT(a.prenom, ' ', a.nom),
                   CONCAT(e.prenom, ' ', e.nom),
                   m.sender_matricule
                 )
               ELSE 'Inconnu'
             END as sender_name,
             u.role as sender_role,
             m.type as message_type
      FROM message m
      LEFT JOIN utilisateur u ON m.sender_matricule = u.matricule
      LEFT JOIN professeur p ON m.sender_matricule = p.matricule
      LEFT JOIN admin a ON m.sender_matricule = a.matricule
      LEFT JOIN eleve e ON m.sender_matricule = e.matricule
      WHERE
        -- Private messages between the two users
        ((m.sender_matricule = ? AND m.receiver_matricule = ?) OR
         (m.sender_matricule = ? AND m.receiver_matricule = ?))
        -- OR announcements from this specific sender to classes taught by current user (for professors)
        OR (m.sender_matricule = ? AND m.type = 'annonce' AND m.receiver_matricule IS NULL
            AND m.nom_classe IN (
              SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
            ))
        -- OR announcements to a specific class (for admins clicking on class conversations)
        OR (m.nom_classe = ? AND m.type = 'annonce' AND m.receiver_matricule IS NULL)
      ORDER BY m.date_envoi ASC
    `;

    const messages = await query(messagesQuery, [
      matricule, matricule, // For sender name logic
      matricule, participantId, // First direction of private messages
      participantId, matricule, // Second direction of private messages
      participantId, matricule, // For announcements from this sender to professor's classes
      participantId // For announcements to a specific class (admin case)
    ]);

    console.log('📧 Found messages:', messages.length, messages);

    // Mark messages as read
    if (messages.length > 0) {
      const markReadQuery = `
        UPDATE message
        SET lu = TRUE
        WHERE (receiver_matricule = ? AND sender_matricule = ?)
           OR (sender_matricule = ? AND type = 'annonce' AND receiver_matricule IS NULL
               AND nom_classe IN (
                 SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
               ))
           OR (nom_classe = ? AND type = 'annonce' AND receiver_matricule IS NULL)
      `;
      await query(markReadQuery, [matricule, participantId, participantId, matricule, participantId]);
    }

    res.json({
      success: true,
      messages: messages
    });

  } catch (error) {
    console.error('❌ Error loading conversation messages:', error);
    res.status(500).json({ success: false, message: 'Erreur lors du chargement des messages' });
  }
});

// New compose modal send endpoint
router.post('/send', hybridAuth(), upload.array('attachments', 5), async (req, res) => {
  console.log('📝 Send message request received');
  console.log('📋 Request body:', req.body);
  console.log('📋 Request files:', req.files);

  const { matricule: sender_matricule } = req.user;
  const { type, receiver_matricule, nom_classe, contenu, subject, priority, recipients } = req.body;

  console.log('🔍 Extracted data:', { sender_matricule, type, receiver_matricule, nom_classe, contenu, subject, priority, recipients });

  if (!sender_matricule || !contenu || !type) {
    console.log('❌ Missing required fields:', {
      sender_matricule: !!sender_matricule,
      contenu: !!contenu,
      type: !!type
    });
    return res.status(400).json({ success: false, message: 'Champs requis manquants' });
  }

  try {
    if (type === 'privé') {
      // Send private message - handle recipients array
      const recipientsList = recipients && recipients.length > 0 ? recipients : [receiver_matricule];

      console.log('📤 Sending private messages to recipients:', recipientsList);

      const sql = `
        INSERT INTO message (sender_matricule, receiver_matricule, contenu, type, subject, priority)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      // Send message to each recipient
      for (const recipient of recipientsList) {
        if (recipient && recipient.trim()) {
          console.log('📨 Sending to:', recipient);
          await query(sql, [sender_matricule, recipient.trim(), contenu, type, subject || null, priority || 'normal']);
        }
      }
    } else if (type === 'annonce') {
      // Send announcement to class
      const sql = `
        INSERT INTO message (sender_matricule, nom_classe, contenu, type, subject, priority)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      await query(sql, [sender_matricule, nom_classe, contenu, type, subject || null, priority || 'normal']);
    }

    res.json({ success: true, message: 'Message envoyé avec succès' });

  } catch (error) {
    console.error('❌ Error sending message:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de l\'envoi du message' });
  }
});

// Quick message send for active conversations
router.post('/send-quick', hybridAuth(), async (req, res) => {
  const { matricule: sender_matricule } = req.user;
  const { conversation_id, contenu, type } = req.body;

  if (!sender_matricule || !contenu || !conversation_id) {
    return res.status(400).json({ success: false, message: 'Champs requis manquants' });
  }

  try {
    const sql = `
      INSERT INTO message (sender_matricule, receiver_matricule, contenu, type)
      VALUES (?, ?, ?, ?)
    `;
    await query(sql, [sender_matricule, conversation_id, contenu, type || 'privé']);

    res.json({ success: true, message: 'Message envoyé avec succès' });

  } catch (error) {
    console.error('❌ Error sending quick message:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de l\'envoi du message' });
  }
});



// Get available classes for announcements
router.get('/classes', async (req, res) => {
  try {
    const classesQuery = `SELECT nom_classe FROM classe ORDER BY nom_classe`;
    const classes = await query(classesQuery);

    res.json({
      success: true,
      classes: classes
    });

  } catch (error) {
    console.error('❌ Error loading classes:', error);
    res.status(500).json({ success: false, message: 'Erreur lors du chargement des classes' });
  }
});

// Mark conversation as read
router.post('/conversation/:conversationId/read-all', hybridAuth(), async (req, res) => {
  const { matricule } = req.user;
  const conversationId = req.params.conversationId;

  if (!matricule) {
    return res.status(401).json({ success: false, message: 'Accès non autorisé' });
  }

  try {
    const updateQuery = `
      UPDATE message
      SET lu = TRUE
      WHERE receiver_matricule = ? AND sender_matricule = ? AND lu = FALSE
    `;
    await query(updateQuery, [matricule, conversationId]);

    res.json({ success: true, message: 'Messages marqués comme lus' });

  } catch (error) {
    console.error('❌ Error marking messages as read:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de la mise à jour' });
  }
});

// Archive conversation
router.post('/conversation/:conversationId/archive', hybridAuth(), async (req, res) => {
  const { matricule } = req.user;
  const conversationId = req.params.conversationId;

  if (!matricule) {
    return res.status(401).json({ success: false, message: 'Accès non autorisé' });
  }

  try {
    // For now, we'll just mark messages as archived (you might want to add an archived column)
    const updateQuery = `
      UPDATE message
      SET lu = TRUE
      WHERE (sender_matricule = ? AND receiver_matricule = ?)
         OR (sender_matricule = ? AND receiver_matricule = ?)
    `;
    await query(updateQuery, [matricule, conversationId, conversationId, matricule]);

    res.json({ success: true, message: 'Conversation archivée' });

  } catch (error) {
    console.error('❌ Error archiving conversation:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de l\'archivage' });
  }
});

// Delete conversation
router.delete('/conversation/:conversationId', hybridAuth(), async (req, res) => {
  const { matricule } = req.user;
  const conversationId = req.params.conversationId;

  if (!matricule) {
    return res.status(401).json({ success: false, message: 'Accès non autorisé' });
  }

  try {
    const deleteQuery = `
      DELETE FROM message
      WHERE (sender_matricule = ? AND receiver_matricule = ?)
         OR (sender_matricule = ? AND receiver_matricule = ?)
    `;
    await query(deleteQuery, [matricule, conversationId, conversationId, matricule]);

    res.json({ success: true, message: 'Conversation supprimée' });

  } catch (error) {
    console.error('❌ Error deleting conversation:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de la suppression' });
  }
});

module.exports = router;

/**
 * Mobile-Compatible Messaging API Routes for NS School Manager
 * Provides clean JSON API endpoints for mobile messaging functionality
 */

const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { apiAuth } = require('../../middlewares/hybridAuth');
const { sendSuccess, sendError, asyncHandler } = require('../../utils/apiResponse');

/**
 * Get conversations for the authenticated user
 * GET /api/mobile/messages/conversations
 */
router.get('/conversations', apiAuth(), asyncHandler(async (req, res) => {
  const { matricule, role } = req.user;

  let conversationsQuery;
  let queryParams;

  if (role === 'admin') {
    // Ad<PERSON> can see all conversations (private messages and announcements)
    conversationsQuery = `
      SELECT DISTINCT
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.nom_classe
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as participant_id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN
            CONCAT('Classe ', m.nom_classe)
          WHEN m.sender_matricule = ? THEN
            COALESCE(CONCAT(pr.prenom, ' ', pr.nom), ar.nom, m.receiver_matricule, 'Utilisateur')
          ELSE
            COALESCE(CONCAT(ps.prenom, ' ', ps.nom), ads.nom, m.sender_matricule, 'Utilisateur')
        END as participant_name,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN 'classe'
          WHEN m.sender_matricule = ? THEN ur.role
          ELSE us.role
        END as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        SUBSTRING(m.contenu, 1, 100) as last_message,
        COUNT(CASE
          WHEN m.lu = FALSE AND m.sender_matricule != ? THEN 1
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.lu = FALSE THEN 1
        END) as unread_count
      FROM message m
      LEFT JOIN utilisateur us ON m.sender_matricule = us.matricule
      LEFT JOIN utilisateur ur ON m.receiver_matricule = ur.matricule
      LEFT JOIN professeur ps ON m.sender_matricule = ps.matricule
      LEFT JOIN professeur pr ON m.receiver_matricule = pr.matricule
      LEFT JOIN admin ads ON m.sender_matricule = ads.matricule
      LEFT JOIN admin ar ON m.receiver_matricule = ar.matricule
      WHERE (m.sender_matricule = ? OR m.receiver_matricule = ?)
         OR (m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.sender_matricule = ?)
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;
    queryParams = [matricule, matricule, matricule, matricule, matricule, matricule, matricule];
  } else if (role === 'professeur') {
    // Professor can see private messages and announcements
    conversationsQuery = `
      SELECT DISTINCT
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN m.sender_matricule
          WHEN m.sender_matricule = ? THEN m.receiver_matricule
          ELSE m.sender_matricule
        END as participant_id,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN
            COALESCE(CONCAT(a.prenom, ' ', a.nom), a.matricule, 'Administration')
          WHEN m.sender_matricule = ? THEN
            COALESCE(CONCAT(ar.prenom, ' ', ar.nom), ar.nom, m.receiver_matricule, 'Utilisateur')
          ELSE
            COALESCE(CONCAT(as.prenom, ' ', as.nom), as.nom, m.sender_matricule, 'Utilisateur')
        END as participant_name,
        CASE
          WHEN m.type = 'annonce' AND m.receiver_matricule IS NULL THEN 'admin'
          WHEN m.sender_matricule = ? THEN ur.role
          ELSE us.role
        END as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        SUBSTRING(m.contenu, 1, 100) as last_message,
        COUNT(CASE WHEN m.lu = FALSE AND m.sender_matricule != ? THEN 1 END) as unread_count
      FROM message m
      LEFT JOIN utilisateur us ON m.sender_matricule = us.matricule
      LEFT JOIN utilisateur ur ON m.receiver_matricule = ur.matricule
      LEFT JOIN admin a ON m.sender_matricule = a.matricule
      LEFT JOIN admin as ON m.sender_matricule = as.matricule
      LEFT JOIN admin ar ON m.receiver_matricule = ar.matricule
      WHERE (m.sender_matricule = ? OR m.receiver_matricule = ?)
         OR (m.type = 'annonce' AND m.receiver_matricule IS NULL AND m.nom_classe IN (
           SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
         ))
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;
    queryParams = [matricule, matricule, matricule, matricule, matricule, matricule, matricule];
  } else if (role === 'eleve') {
    // Student can only see messages from admin and announcements for their class
    conversationsQuery = `
      SELECT
        m.sender_matricule as participant_id,
        COALESCE(a.nom, 'Administration') as participant_name,
        'admin' as participant_role,
        m.type,
        MAX(m.date_envoi) as last_message_time,
        SUBSTRING(m.contenu, 1, 100) as last_message,
        COUNT(CASE WHEN m.lu = FALSE THEN 1 END) as unread_count
      FROM message m
      LEFT JOIN admin a ON m.sender_matricule = a.matricule
      WHERE m.receiver_matricule = ? OR (m.type = 'annonce' AND m.nom_classe IN (
        SELECT nom_classe FROM eleve WHERE matricule = ?
      ))
      GROUP BY participant_id, participant_name, participant_role, m.type
      ORDER BY last_message_time DESC
    `;
    queryParams = [matricule, matricule];
  } else {
    return sendError(res, 'Rôle non autorisé', 403);
  }

  const conversations = await query(conversationsQuery, queryParams);
  
  // Get message statistics
  const statsQuery = `
    SELECT
      COUNT(CASE WHEN lu = FALSE AND receiver_matricule = ? THEN 1 END) as unread,
      COUNT(CASE WHEN type = 'privé' AND (sender_matricule = ? OR receiver_matricule = ?) THEN 1 END) as private_messages,
      COUNT(CASE WHEN type = 'annonce' AND sender_matricule = ? THEN 1 END) as announcements_sent,
      COUNT(CASE WHEN type = 'annonce' AND receiver_matricule = ? THEN 1 END) as announcements_received
    FROM message
  `;
  
  const statsResult = await query(statsQuery, [matricule, matricule, matricule, matricule, matricule]);
  const stats = statsResult[0] || {};

  return sendSuccess(res, {
    conversations,
    stats
  }, 'Conversations récupérées avec succès');
}));

/**
 * Get messages for a specific conversation
 * GET /api/mobile/messages/conversation/:participantId
 */
router.get('/conversation/:participantId', apiAuth(), asyncHandler(async (req, res) => {
  const { matricule } = req.user;
  const { participantId } = req.params;

  const messagesQuery = `
    SELECT m.*,
           CASE
             WHEN m.sender_matricule = ? THEN 'Vous'
             WHEN m.sender_matricule != ? THEN
               COALESCE(
                 CONCAT(p.prenom, ' ', p.nom),
                 CONCAT(a.prenom, ' ', a.nom),
                 CONCAT(e.prenom, ' ', e.nom),
                 m.sender_matricule
               )
             ELSE 'Inconnu'
           END as sender_name,
           u.role as sender_role,
           m.type as message_type
    FROM message m
    LEFT JOIN utilisateur u ON m.sender_matricule = u.matricule
    LEFT JOIN professeur p ON m.sender_matricule = p.matricule
    LEFT JOIN admin a ON m.sender_matricule = a.matricule
    LEFT JOIN eleve e ON m.sender_matricule = e.matricule
    WHERE
      ((m.sender_matricule = ? AND m.receiver_matricule = ?) OR
       (m.sender_matricule = ? AND m.receiver_matricule = ?))
      OR (m.sender_matricule = ? AND m.type = 'annonce' AND m.receiver_matricule IS NULL
          AND m.nom_classe IN (
            SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
          ))
      OR (m.nom_classe = ? AND m.type = 'annonce' AND m.receiver_matricule IS NULL)
    ORDER BY m.date_envoi ASC
  `;

  const messages = await query(messagesQuery, [
    // sender_name CASE
    matricule, matricule,
    // private both directions
    matricule, participantId,
    participantId, matricule,
    // announcements from participant to professor's classes
    participantId, matricule,
    // admin viewing class conversation
    participantId
  ]);

  console.log('📧 Found messages (mobile):', messages.length);

  // Mark messages as read for private messages and relevant announcements
  if (messages.length > 0) {
    const markReadQuery = `
      UPDATE message
      SET lu = TRUE
      WHERE (receiver_matricule = ? AND sender_matricule = ?)
         OR (sender_matricule = ? AND type = 'annonce' AND receiver_matricule IS NULL
             AND nom_classe IN (
               SELECT nom_classe FROM enseignant_classe WHERE professeur_matricule = ?
             ))
         OR (nom_classe = ? AND type = 'annonce' AND receiver_matricule IS NULL)
    `;
    await query(markReadQuery, [matricule, participantId, participantId, matricule, participantId]);
  }

  return sendSuccess(res, { messages }, 'Messages récupérés avec succès');
}));

/**
 * Send a new message
 * POST /api/mobile/messages/send
 */
router.post('/send', apiAuth(), asyncHandler(async (req, res) => {
  const { matricule: sender_matricule } = req.user;
  const { type, recipients, nom_classe, subject, contenu, priority } = req.body;

  if (!contenu || contenu.trim().length === 0) {
    return sendError(res, 'Le contenu du message est requis', 400);
  }

  if (type === 'privé') {
    if (!recipients || recipients.length === 0) {
      return sendError(res, 'Au moins un destinataire est requis pour un message privé', 400);
    }

    const sql = `
      INSERT INTO message (sender_matricule, receiver_matricule, contenu, type, subject, priority)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    // Send message to each recipient
    for (const recipient of recipients) {
      if (recipient && recipient.trim()) {
        await query(sql, [sender_matricule, recipient.trim(), contenu, type, subject || null, priority || 'normal']);
      }
    }
  } else if (type === 'annonce') {
    if (!nom_classe) {
      return sendError(res, 'La classe est requise pour une annonce', 400);
    }

    const sql = `
      INSERT INTO message (sender_matricule, nom_classe, contenu, type, subject, priority)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    await query(sql, [sender_matricule, nom_classe, contenu, type, subject || null, priority || 'normal']);
  } else {
    return sendError(res, 'Type de message invalide', 400);
  }

  return sendSuccess(res, null, 'Message envoyé avec succès');
}));

/**
 * Send a quick reply to an existing conversation
 * POST /api/mobile/messages/reply
 */
router.post('/reply', apiAuth(), asyncHandler(async (req, res) => {
  const { matricule: sender_matricule, role } = req.user;
  const { participant_id, contenu } = req.body;

  if (!participant_id || !contenu || contenu.trim().length === 0) {
    return sendError(res, 'Destinataire et contenu requis', 400);
  }

  // If replying to a class thread (e.g., "6eme B"), save as an announcement instead of a private message
  // This prevents FK violation on receiver_matricule and matches how class threads are represented.
  if (role === 'admin') {
    // Determine if participant_id is a valid user matricule
    const userRows = await query('SELECT matricule FROM utilisateur WHERE matricule = ?', [participant_id]);
    const isUserMatricule = userRows && userRows.length > 0;

    if (!isUserMatricule) {
      // Treat as class name announcement
      const sqlAnnonce = `
        INSERT INTO message (sender_matricule, nom_classe, contenu, type, subject, priority)
        VALUES (?, ?, ?, 'annonce', NULL, 'normal')
      `;
      await query(sqlAnnonce, [sender_matricule, participant_id, contenu.trim()]);
      return sendSuccess(res, null, 'Annonce envoyée avec succès');
    }
  }

  // Default: private message to a user matricule
  const sql = `
    INSERT INTO message (sender_matricule, receiver_matricule, contenu, type)
    VALUES (?, ?, ?, 'privé')
  `;
  await query(sql, [sender_matricule, participant_id, contenu.trim()]);

  return sendSuccess(res, null, 'Réponse envoyée avec succès');
}));

/**
 * Get available recipients (for compose message)
 * GET /api/mobile/messages/recipients
 */
router.get('/recipients', apiAuth(), asyncHandler(async (req, res) => {
  const { role } = req.user;

  let recipients = [];
  let classes = [];

  if (role === 'admin') {
    // Admin can message professors and get all classes
    const profQuery = `
      SELECT p.matricule, CONCAT(p.prenom, ' ', p.nom) as nom, 'professeur' as role
      FROM professeur p
      JOIN utilisateur u ON p.matricule = u.matricule
      WHERE u.role = 'professeur'
      ORDER BY p.nom
    `;
    recipients = await query(profQuery);

    const classQuery = `SELECT nom_classe FROM classe ORDER BY nom_classe`;
    classes = await query(classQuery);
  }

  return sendSuccess(res, { recipients, classes }, 'Destinataires récupérés avec succès');
}));

module.exports = router;
